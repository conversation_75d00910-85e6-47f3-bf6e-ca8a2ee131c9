/* CSS变量定义 */
:root {
  /* 主色调 */
  --primary-color: #40C4FF;
  --secondary-color: #007BFF;

  /* 背景色 */
  --bg-main: #0A1122; /* 极暗近黑蓝 */
  --bg-panel: #101A30; /* 稍亮深蓝灰 */
  --bg-hover: #1A2B4C; /* 悬停/高亮背景 */
  --bg-glass: rgba(16, 26, 48, 0.5); /* 50%磨玻璃效果 */

  /* 文本颜色 */
  --text-primary: #EAEAEA; /* 主文本 - 浅灰白 */
  --text-secondary: #B0BEC5; /* 次要文本/标签 - 蓝灰 */
  --text-icon: #BDC3C7; /* 图标默认颜色 */

  /* 状态色 */
  --status-critical: #FF4D4F; /* 重大风险 - 亮红 */
  --status-major: #FF8600; /* 较大风险 - 橙色 */
  --status-moderate: #FFC107; /* 一般风险 - 黄色 */
  --status-minor: #40C4FF; /* 较小风险 - 亮天蓝 */
  --status-normal: #4CAF50; /* 正常状态 - 绿色 */

  /* 边框和间距 */
  --border-color: #566573;
  --border-radius: 2px;
  --spacing-base: 10px;
  --spacing-panel: 15px;

  /* 字体 */
  --font-family: 'PingFang SC', 'Source Han Sans SC', sans-serif;
  --font-size-base: 14px;
  --font-size-h1: 30px;
  --font-size-h2: 18px;
  --font-size-h3: 16px;
  --font-weight-normal: 400;
  --font-weight-medium: 500;
}

/* 全局样式重置 */
* {
  margin: 0;
  padding: 0;
  box-sizing: border-box;
}

html, body {
  width: 100%;
  height: 100%;
  overflow: hidden;
  font-family: var(--font-family);
  font-size: var(--font-size-base);
  font-weight: var(--font-weight-normal);
  background-color: var(--bg-main);
  color: var(--text-primary);
}

#app {
  width: 100vw;
  height: 100vh;
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 16:9自适应容器 */
.aspect-ratio-container {
  width: 100vw;
  height: 100vh;
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

/* 16:9比例内容 */
.aspect-ratio-content {
  width: 100%;
  height: 100%;
  max-width: 177.78vh; /* 16:9 */
  max-height: 56.25vw; /* 9:16 */
  overflow: hidden;
}

/* 磨玻璃效果 */
.glass-effect {
  background: var(--bg-panel);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  border: 1px solid var(--border-color);
}

/* 布局样式 */
.dashboard-layout {
  display: flex;
  flex-direction: column;
  height: 100vh;
  width: 100vw;
}

.dashboard-header {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  z-index: 1000;
  display: flex;
  align-items: center;
  justify-content: center;
  padding: 0 var(--spacing-panel);
}

.dashboard-content {
  display: flex;
  height: calc(100vh - 80px - 120px);
  margin-top: 80px;
  margin-bottom: 120px;
}

.left-panel, .right-panel {
  width: 320px;
  padding: var(--spacing-base);
  overflow: hidden;
  flex-shrink: 0;
}

.center-area {
  width: 50%;
  padding: var(--spacing-panel);
  display: flex;
  flex-direction: column;
}

/* 数据模块样式 */
.data-module {
  margin-bottom: var(--spacing-panel);
  padding: var(--spacing-panel);
  border-radius: var(--border-radius);
}

.module-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  margin-bottom: var(--spacing-base);
  color: var(--primary-color);
}

.module-content {
  color: var(--text-secondary);
}

/* 响应式设计 */
@media (max-width: 1920px) {
  .dashboard-content {
    transform: scale(0.9);
    transform-origin: top left;
  }
}

@media (max-width: 1600px) {
  .dashboard-content {
    transform: scale(0.8);
    transform-origin: top left;
  }
}

@media (max-width: 1366px) {
  .dashboard-content {
    transform: scale(0.7);
    transform-origin: top left;
  }
}

/* 完全禁用滚动条 */
* {
  scrollbar-width: none;
  -ms-overflow-style: none;
}

*::-webkit-scrollbar {
  display: none;
}

/* 确保所有元素不产生滚动 */
* {
  overflow: visible;
}

.dashboard-layout,
.dashboard-content,
.left-panel,
.right-panel,
.center-area {
  overflow: hidden !important;
}

/* 选择文本样式 */
::selection {
  background: var(--primary-color);
  color: white;
}

/* 动画效果 */
@keyframes fadeIn {
  from {
    opacity: 0;
    transform: translateY(20px);
  }
  to {
    opacity: 1;
    transform: translateY(0);
  }
}

.data-module {
  animation: fadeIn 0.6s ease-out;
}

.data-module:nth-child(1) { animation-delay: 0.1s; }
.data-module:nth-child(2) { animation-delay: 0.2s; }
.data-module:nth-child(3) { animation-delay: 0.3s; }

/* 鼠标悬停效果 */
.data-module:hover {
  transform: translateY(-2px);
  box-shadow: 0 8px 25px rgba(64, 196, 255, 0.15);
  transition: all 0.3s ease;
}
