<template>
  <div class="center-area">
    <!-- 3D模型标题栏 -->
    <div class="model-header glass-effect">
      <div class="model-title">
        <span class="model-icon">🏗️</span>
        电厂施工区三维模型
      </div>
      <div class="model-controls">
        <button class="control-btn" :class="{ active: viewMode === 'overview' }" @click="viewMode = 'overview'">
          总览
        </button>
        <button class="control-btn" :class="{ active: viewMode === 'crane' }" @click="viewMode = 'crane'">
          塔吊区
        </button>
        <button class="control-btn" :class="{ active: viewMode === 'scaffold' }" @click="viewMode = 'scaffold'">
          高支模区
        </button>
        <button class="control-btn" :class="{ active: showPersonnel }" @click="showPersonnel = !showPersonnel">
          人员分布
        </button>
      </div>
    </div>

    <!-- 3D模型主体 -->
    <div class="model-container glass-effect">
      <div class="model-content">
        <!-- 模拟3D场景背景 -->
        <div class="scene-background">
          <div class="scene-grid"></div>

          <!-- 建筑结构 -->
          <div class="building-structures">
            <!-- 主厂房 -->
            <div class="building main-building" :style="{ left: '40%', top: '30%', width: '200px', height: '120px' }">
              <div class="building-label">主厂房</div>
              <div class="building-progress">进度: 75%</div>
            </div>

            <!-- 辅助建筑 -->
            <div class="building aux-building" :style="{ left: '20%', top: '50%', width: '100px', height: '80px' }">
              <div class="building-label">辅助厂房</div>
              <div class="building-progress">进度: 60%</div>
            </div>
          </div>

          <!-- 塔吊设备 -->
          <div class="equipment-layer">
            <div
              v-for="crane in cranes"
              :key="crane.id"
              class="crane-equipment"
              :class="crane.status"
              :style="{ left: crane.x + '%', top: crane.y + '%' }"
              @click="selectEquipment(crane, 'crane')"
            >
              <div class="equipment-icon">🏗️</div>
              <div class="equipment-status" :class="crane.status"></div>
              <div class="equipment-label">{{ crane.name }}</div>
              <div class="equipment-data">
                <div>倾斜: {{ crane.tilt }}°</div>
                <div>负载: {{ crane.load }}%</div>
              </div>

              <!-- GNSS测点 -->
              <div class="gnss-point" @click.stop="showGNSSData(crane)">
                <span class="gnss-icon">📡</span>
              </div>
            </div>
          </div>

          <!-- 高支模区域 -->
          <div class="scaffold-layer">
            <div
              v-for="scaffold in scaffolds"
              :key="scaffold.id"
              class="scaffold-area"
              :class="scaffold.status"
              :style="{ left: scaffold.x + '%', top: scaffold.y + '%', width: scaffold.width + 'px', height: scaffold.height + 'px' }"
              @click="selectEquipment(scaffold, 'scaffold')"
            >
              <div class="scaffold-label">{{ scaffold.name }}</div>
              <div class="scaffold-points">
                <div v-for="point in scaffold.points" :key="point.id"
                     class="measure-point" :class="point.status"
                     :style="{ left: point.x + '%', top: point.y + '%' }"
                     @click.stop="showPointData(point)">
                  <span class="point-icon">📍</span>
                </div>
              </div>
            </div>
          </div>

          <!-- 摄像头图标 -->
          <div class="camera-layer">
            <div
              v-for="camera in cameras"
              :key="camera.id"
              class="camera-point"
              :class="{ active: camera.active }"
              :style="{ left: camera.x + '%', top: camera.y + '%' }"
              @click="openCameraView(camera)"
            >
              <div class="camera-icon">📹</div>
              <div class="camera-label">{{ camera.name }}</div>
            </div>
          </div>

          <!-- 人员分布 -->
          <div class="personnel-layer" v-if="showPersonnel">
            <div
              v-for="area in personnelAreas"
              :key="area.id"
              class="personnel-area"
              :style="{ left: area.x + '%', top: area.y + '%' }"
            >
              <div class="personnel-icon">👥</div>
              <div class="personnel-count">{{ area.count }}人</div>
              <div class="personnel-label">{{ area.name }}</div>
            </div>
          </div>
        </div>

        <!-- 模型图例 -->
        <div class="model-legend">
          <div class="legend-title">设备状态</div>
          <div class="legend-items">
            <div class="legend-item">
              <div class="legend-color normal"></div>
              <span>正常运行</span>
            </div>
            <div class="legend-item">
              <div class="legend-color warning"></div>
              <span>预警状态</span>
            </div>
            <div class="legend-item">
              <div class="legend-color danger"></div>
              <span>报警状态</span>
            </div>
          </div>

          <div class="legend-section">
            <div class="legend-title">测点类型</div>
            <div class="legend-items">
              <div class="legend-item">
                <span class="legend-icon">📡</span>
                <span>GNSS测点</span>
              </div>
              <div class="legend-item">
                <span class="legend-icon">📍</span>
                <span>监测点</span>
              </div>
              <div class="legend-item">
                <span class="legend-icon">📹</span>
                <span>监控摄像头</span>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模型底部信息栏 -->
    <div class="model-footer glass-effect">
      <div class="model-stats">
        <div class="stat-item">
          <span class="stat-label">塔吊设备:</span>
          <span class="stat-value">{{ cranes.length }}台</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">高支模区域:</span>
          <span class="stat-value">{{ scaffolds.length }}个</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">监测点位:</span>
          <span class="stat-value">{{ totalMonitorPoints }}个</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">在场人员:</span>
          <span class="stat-value">{{ totalPersonnel }}人</span>
        </div>
        <div class="stat-item">
          <span class="stat-label">数据更新:</span>
          <span class="stat-value">{{ lastUpdate }}</span>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

const viewMode = ref('overview')
const showPersonnel = ref(false)
const selectedEquipment = ref(null)
const lastUpdate = ref('')

// 塔吊设备数据
const cranes = ref([
  {
    id: 1,
    name: '1号塔吊',
    x: 25,
    y: 35,
    status: 'normal',
    tilt: 0.8,
    load: 45,
    gnssData: { x: 123.456789, y: 34.567890, z: 125.5 }
  },
  {
    id: 2,
    name: '2号塔吊',
    x: 65,
    y: 25,
    status: 'warning',
    tilt: 1.2,
    load: 78,
    gnssData: { x: 123.456823, y: 34.567912, z: 128.2 }
  },
  {
    id: 3,
    name: '3号塔吊',
    x: 75,
    y: 60,
    status: 'normal',
    tilt: 0.5,
    load: 32,
    gnssData: { x: 123.456845, y: 34.567834, z: 122.8 }
  }
])

// 高支模数据
const scaffolds = ref([
  {
    id: 1,
    name: '高支模A区',
    x: 30,
    y: 45,
    width: 80,
    height: 60,
    status: 'normal',
    points: [
      { id: 1, x: 20, y: 30, status: 'normal', type: 'tilt', value: 0.3 },
      { id: 2, x: 80, y: 30, status: 'warning', type: 'settlement', value: 2.5 },
      { id: 3, x: 50, y: 70, status: 'normal', type: 'axial', value: 85 }
    ]
  },
  {
    id: 2,
    name: '高支模B区',
    x: 55,
    y: 70,
    width: 70,
    height: 50,
    status: 'warning',
    points: [
      { id: 4, x: 25, y: 40, status: 'normal', type: 'tilt', value: 0.8 },
      { id: 5, x: 75, y: 40, status: 'danger', type: 'settlement', value: 4.2 },
      { id: 6, x: 50, y: 80, status: 'warning', type: 'axial', value: 95 }
    ]
  }
])

// 摄像头数据
const cameras = ref([
  { id: 1, name: '主入口', x: 15, y: 80, active: true },
  { id: 2, name: '塔吊区域', x: 30, y: 30, active: true },
  { id: 3, name: '高支模区', x: 60, y: 50, active: false },
  { id: 4, name: '材料堆场', x: 80, y: 75, active: true }
])

// 人员分布数据
const personnelAreas = ref([
  { id: 1, name: '主厂房', x: 45, y: 35, count: 28 },
  { id: 2, name: '辅助厂房', x: 25, y: 55, count: 15 },
  { id: 3, name: '材料区', x: 75, y: 70, count: 12 },
  { id: 4, name: '办公区', x: 15, y: 20, count: 8 }
])

// 计算属性
const totalMonitorPoints = computed(() => {
  return scaffolds.value.reduce((total, scaffold) => total + scaffold.points.length, 0) + cranes.value.length
})

const totalPersonnel = computed(() => {
  return personnelAreas.value.reduce((total, area) => total + area.count, 0)
})

// 方法
const selectEquipment = (equipment, type) => {
  selectedEquipment.value = { ...equipment, type }
  console.log(`选中${type}:`, equipment.name)
}

const showGNSSData = (crane) => {
  console.log('GNSS数据:', crane.gnssData)
  // 这里可以打开GNSS数据详情弹窗
}

const showPointData = (point) => {
  console.log('测点数据:', point)
  // 这里可以打开测点数据详情弹窗
}

const openCameraView = (camera) => {
  console.log('打开摄像头:', camera.name)
  // 这里可以打开视频监控窗口
}

const updateTime = () => {
  const now = new Date()
  lastUpdate.value = now.toLocaleTimeString('zh-CN', { hour12: false })
}

// 模拟数据更新
let dataInterval = null

const updateEquipmentData = () => {
  // 更新塔吊数据
  cranes.value.forEach(crane => {
    crane.tilt = Math.random() * 2
    crane.load = Math.floor(Math.random() * 50) + 30

    // 随机改变状态
    const rand = Math.random()
    if (rand > 0.9) crane.status = 'danger'
    else if (rand > 0.7) crane.status = 'warning'
    else crane.status = 'normal'
  })

  // 更新高支模测点数据
  scaffolds.value.forEach(scaffold => {
    scaffold.points.forEach(point => {
      if (point.type === 'tilt') {
        point.value = Math.random() * 2
      } else if (point.type === 'settlement') {
        point.value = Math.random() * 5
      } else if (point.type === 'axial') {
        point.value = Math.floor(Math.random() * 30) + 70
      }

      // 更新状态
      const rand = Math.random()
      if (rand > 0.85) point.status = 'danger'
      else if (rand > 0.7) point.status = 'warning'
      else point.status = 'normal'
    })
  })
}

onMounted(() => {
  updateTime()
  setInterval(updateTime, 1000)
  dataInterval = setInterval(updateEquipmentData, 5000)
})

onUnmounted(() => {
  if (dataInterval) {
    clearInterval(dataInterval)
  }
})
</script>

<style scoped>
.model-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-panel);
  margin-bottom: var(--spacing-base);
  border-radius: var(--border-radius);
}

.model-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
}

.model-icon {
  margin-right: 8px;
  font-size: 16px;
}

.model-controls {
  display: flex;
  gap: var(--spacing-base);
}

.control-btn {
  padding: 5px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover,
.control-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.model-container {
  flex: 1;
  border-radius: var(--border-radius);
  overflow: hidden;
  position: relative;
}

.model-content {
  height: 100%;
  position: relative;
}

.scene-background {
  width: 100%;
  height: 100%;
  background: linear-gradient(135deg, #0A1122 0%, #1A2B4C 100%);
  position: relative;
  overflow: hidden;
}

.scene-grid {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background-image:
    linear-gradient(rgba(64, 196, 255, 0.05) 1px, transparent 1px),
    linear-gradient(90deg, rgba(64, 196, 255, 0.05) 1px, transparent 1px);
  background-size: 30px 30px;
  animation: gridMove 30s linear infinite;
}

@keyframes gridMove {
  0% { transform: translate(0, 0); }
  100% { transform: translate(30px, 30px); }
}

/* 建筑结构样式 */
.building-structures {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 1;
}

.building {
  position: absolute;
  background: linear-gradient(45deg, rgba(64, 196, 255, 0.2), rgba(64, 196, 255, 0.1));
  border: 2px solid var(--primary-color);
  border-radius: 4px;
  display: flex;
  flex-direction: column;
  justify-content: center;
  align-items: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.building:hover {
  transform: scale(1.05);
  box-shadow: 0 0 20px rgba(64, 196, 255, 0.5);
}

.building-label {
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 5px;
}

.building-progress {
  font-size: 10px;
  color: var(--text-secondary);
}

/* 设备层样式 */
.equipment-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.crane-equipment {
  position: absolute;
  cursor: pointer;
  transform: translate(-50%, -50%);
  text-align: center;
}

.equipment-icon {
  font-size: 24px;
  margin-bottom: 5px;
  filter: drop-shadow(0 0 5px rgba(0, 0, 0, 0.8));
}

.equipment-status {
  position: absolute;
  top: -5px;
  right: -5px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  border: 2px solid white;
}

.equipment-status.normal { background: var(--status-normal); }
.equipment-status.warning { background: var(--status-major); }
.equipment-status.danger { background: var(--status-critical); }

.equipment-label {
  font-size: 11px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  margin-bottom: 3px;
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

.equipment-data {
  font-size: 9px;
  color: var(--text-secondary);
  line-height: 1.2;
}

.gnss-point {
  position: absolute;
  top: -8px;
  left: -8px;
  width: 16px;
  height: 16px;
  background: rgba(64, 196, 255, 0.8);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  animation: pulse 2s infinite;
}

.gnss-icon {
  font-size: 8px;
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  100% {
    transform: scale(1.5);
    opacity: 0;
  }
}

/* 高支模样式 */
.scaffold-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 2;
}

.scaffold-area {
  position: absolute;
  border: 2px dashed var(--primary-color);
  background: rgba(64, 196, 255, 0.1);
  border-radius: 4px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.scaffold-area:hover {
  background: rgba(64, 196, 255, 0.2);
}

.scaffold-area.warning {
  border-color: var(--status-major);
  background: rgba(255, 134, 0, 0.1);
}

.scaffold-area.danger {
  border-color: var(--status-critical);
  background: rgba(255, 77, 79, 0.1);
}

.scaffold-label {
  position: absolute;
  top: 5px;
  left: 5px;
  font-size: 10px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  background: rgba(0, 0, 0, 0.5);
  padding: 2px 6px;
  border-radius: 2px;
}

.scaffold-points {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
}

.measure-point {
  position: absolute;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  cursor: pointer;
  display: flex;
  align-items: center;
  justify-content: center;
  transform: translate(-50%, -50%);
}

.measure-point.normal { background: var(--status-normal); }
.measure-point.warning { background: var(--status-major); }
.measure-point.danger { background: var(--status-critical); }

.point-icon {
  font-size: 8px;
  color: white;
}

/* 摄像头样式 */
.camera-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
}

.camera-point {
  position: absolute;
  cursor: pointer;
  transform: translate(-50%, -50%);
  text-align: center;
}

.camera-icon {
  font-size: 18px;
  margin-bottom: 3px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8));
}

.camera-point.active .camera-icon {
  color: var(--status-normal);
}

.camera-point:not(.active) .camera-icon {
  color: #666;
}

.camera-label {
  font-size: 9px;
  color: var(--text-secondary);
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

/* 人员分布样式 */
.personnel-layer {
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  z-index: 3;
}

.personnel-area {
  position: absolute;
  cursor: pointer;
  transform: translate(-50%, -50%);
  text-align: center;
}

.personnel-icon {
  font-size: 20px;
  margin-bottom: 3px;
  filter: drop-shadow(0 0 3px rgba(0, 0, 0, 0.8));
}

.personnel-count {
  font-size: 11px;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

.personnel-label {
  font-size: 9px;
  color: var(--text-secondary);
  text-shadow: 0 0 3px rgba(0, 0, 0, 0.8);
}

/* 图例样式 */
.model-legend {
  position: absolute;
  top: 20px;
  right: 20px;
  background: rgba(16, 26, 48, 0.9);
  padding: var(--spacing-base);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
  max-width: 150px;
}

.legend-title {
  font-size: 12px;
  color: var(--text-primary);
  margin-bottom: 8px;
  font-weight: var(--font-weight-medium);
}

.legend-section {
  margin-top: var(--spacing-base);
}

.legend-items {
  margin-bottom: 5px;
}

.legend-item {
  display: flex;
  align-items: center;
  margin-bottom: 5px;
  font-size: 10px;
  color: var(--text-secondary);
}

.legend-color {
  width: 10px;
  height: 10px;
  border-radius: 50%;
  margin-right: 6px;
}

.legend-color.normal { background: var(--status-normal); }
.legend-color.warning { background: var(--status-major); }
.legend-color.danger { background: var(--status-critical); }

.legend-icon {
  margin-right: 6px;
  font-size: 10px;
}

/* 底部信息栏样式 */
.model-footer {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-base) var(--spacing-panel);
  margin-top: var(--spacing-base);
  border-radius: var(--border-radius);
}

.model-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  font-size: 12px;
}

.stat-label {
  color: var(--text-secondary);
  margin-right: 5px;
}

.stat-value {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}
</style>
