<template>
  <div class="alert-center glass-effect">
    <div class="alert-header">
      <div class="header-left">
        <span class="alert-icon">🚨</span>
        <span class="alert-title">告警与事件中心</span>
      </div>
      
      <div class="header-center">
        <div class="alert-stats">
          <div class="stat-item level-1">
            <span class="stat-label">I级告警</span>
            <span class="stat-count">{{ level1Count }}</span>
          </div>
          <div class="stat-item level-2">
            <span class="stat-label">II级告警</span>
            <span class="stat-count">{{ level2Count }}</span>
          </div>
          <div class="stat-item level-3">
            <span class="stat-label">III级告警</span>
            <span class="stat-count">{{ level3Count }}</span>
          </div>
        </div>
      </div>
      
      <div class="header-right">
        <button class="control-btn" :class="{ active: autoScroll }" @click="toggleAutoScroll">
          {{ autoScroll ? '暂停滚动' : '自动滚动' }}
        </button>
        <button class="control-btn" @click="clearAlerts">
          清空告警
        </button>
      </div>
    </div>
    
    <div class="alert-content">
      <div class="alert-stream" ref="alertStream" :class="{ scrolling: autoScroll }">
        <div v-for="alert in alerts" :key="alert.id" 
             class="alert-item" 
             :class="[alert.level, { clickable: alert.linkable }]"
             @click="handleAlertClick(alert)">
          
          <div class="alert-time">{{ alert.time }}</div>
          
          <div class="alert-level-badge" :class="alert.level">
            {{ getLevelText(alert.level) }}
          </div>
          
          <div class="alert-source">{{ alert.source }}</div>
          
          <div class="alert-message">{{ alert.message }}</div>
          
          <div class="alert-actions">
            <button v-if="alert.linkable" class="action-btn link" @click.stop="linkToModel(alert)">
              定位
            </button>
            <button v-if="alert.hasVideo" class="action-btn video" @click.stop="openVideo(alert)">
              视频
            </button>
            <button class="action-btn details" @click.stop="showDetails(alert)">
              详情
            </button>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick } from 'vue'

const alertStream = ref(null)
const autoScroll = ref(true)

// 告警数据
const alerts = ref([
  {
    id: 1,
    time: '14:35:23',
    level: 'level-1',
    source: '高支模3号测点',
    message: '轴力超过预警值，当前值：98kN，预警阈值：95kN',
    linkable: true,
    hasVideo: true,
    details: { location: '高支模B区', value: 98, threshold: 95 }
  },
  {
    id: 2,
    time: '14:33:15',
    level: 'level-2',
    source: '1号塔吊',
    message: '倾斜度接近报警阈值，当前值：1.4°，报警阈值：1.5°',
    linkable: true,
    hasVideo: true,
    details: { location: '塔吊区域', value: 1.4, threshold: 1.5 }
  },
  {
    id: 3,
    time: '14:30:42',
    level: 'level-1',
    source: '南门门禁',
    message: '发生非法闯入告警，检测到未授权人员进入',
    linkable: false,
    hasVideo: true,
    details: { location: '南门区域', person: '未知人员' }
  },
  {
    id: 4,
    time: '14:28:18',
    level: 'level-3',
    source: '气象监测',
    message: '风速达到预警级别，当前风速：18m/s',
    linkable: false,
    hasVideo: false,
    details: { windSpeed: 18, threshold: 15 }
  },
  {
    id: 5,
    time: '14:25:33',
    level: 'level-2',
    source: '高支模5号测点',
    message: '沉降量超过预警值，当前值：4.2mm，预警阈值：4.0mm',
    linkable: true,
    hasVideo: false,
    details: { location: '高支模B区', value: 4.2, threshold: 4.0 }
  },
  {
    id: 6,
    time: '14:22:07',
    level: 'level-3',
    source: '2号塔吊',
    message: '负载率较高，当前负载：85%',
    linkable: true,
    hasVideo: true,
    details: { location: '塔吊区域', load: 85 }
  }
])

// 计算各级别告警数量
const level1Count = computed(() => alerts.value.filter(a => a.level === 'level-1').length)
const level2Count = computed(() => alerts.value.filter(a => a.level === 'level-2').length)
const level3Count = computed(() => alerts.value.filter(a => a.level === 'level-3').length)

// 方法
const getLevelText = (level) => {
  const levelMap = {
    'level-1': 'I级',
    'level-2': 'II级',
    'level-3': 'III级'
  }
  return levelMap[level] || level
}

const toggleAutoScroll = () => {
  autoScroll.value = !autoScroll.value
}

const clearAlerts = () => {
  if (confirm('确定要清空所有告警信息吗？')) {
    alerts.value = []
  }
}

const handleAlertClick = (alert) => {
  if (alert.linkable) {
    linkToModel(alert)
  }
}

const linkToModel = (alert) => {
  console.log('定位到3D模型:', alert.source)
  // 这里可以触发3D模型的定位功能
}

const openVideo = (alert) => {
  console.log('打开视频监控:', alert.source)
  // 这里可以打开相关的视频监控窗口
}

const showDetails = (alert) => {
  console.log('显示详情:', alert.details)
  // 这里可以打开详情弹窗
}

// 自动滚动功能
let scrollInterval = null

const startAutoScroll = () => {
  if (scrollInterval) return
  
  scrollInterval = setInterval(() => {
    if (autoScroll.value && alertStream.value) {
      const container = alertStream.value
      const scrollAmount = 1
      container.scrollLeft += scrollAmount
      
      // 如果滚动到末尾，重新开始
      if (container.scrollLeft >= container.scrollWidth - container.clientWidth) {
        container.scrollLeft = 0
      }
    }
  }, 50)
}

const stopAutoScroll = () => {
  if (scrollInterval) {
    clearInterval(scrollInterval)
    scrollInterval = null
  }
}

// 模拟新告警
const addNewAlert = () => {
  const newAlert = {
    id: Date.now(),
    time: new Date().toLocaleTimeString('zh-CN', { hour12: false }),
    level: ['level-1', 'level-2', 'level-3'][Math.floor(Math.random() * 3)],
    source: ['塔吊设备', '高支模测点', '门禁系统', '气象监测'][Math.floor(Math.random() * 4)],
    message: '新的告警信息...',
    linkable: Math.random() > 0.5,
    hasVideo: Math.random() > 0.5,
    details: {}
  }
  
  alerts.value.unshift(newAlert)
  
  // 限制告警数量
  if (alerts.value.length > 20) {
    alerts.value = alerts.value.slice(0, 20)
  }
}

onMounted(() => {
  startAutoScroll()
  
  // 模拟新告警产生
  setInterval(addNewAlert, 15000)
})

onUnmounted(() => {
  stopAutoScroll()
})
</script>

<style scoped>
.alert-center {
  position: fixed;
  bottom: 0;
  left: 0;
  right: 0;
  height: 120px;
  z-index: 1000;
  border-radius: var(--border-radius) var(--border-radius) 0 0;
  border-bottom: none;
}

.alert-header {
  display: flex;
  align-items: center;
  justify-content: space-between;
  padding: var(--spacing-base) var(--spacing-panel);
  border-bottom: 1px solid var(--border-color);
}

.header-left {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.alert-icon {
  font-size: 18px;
  color: var(--status-critical);
}

.alert-title {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.header-center {
  flex: 1;
  display: flex;
  justify-content: center;
}

.alert-stats {
  display: flex;
  gap: 20px;
}

.stat-item {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 12px;
}

.stat-item.level-1 {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.stat-item.level-2 {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.stat-item.level-3 {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.stat-label {
  font-weight: var(--font-weight-medium);
}

.stat-count {
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
}

.header-right {
  display: flex;
  gap: var(--spacing-base);
}

.control-btn {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  color: var(--text-secondary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.control-btn:hover,
.control-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.alert-content {
  height: calc(100% - 50px);
  overflow: hidden;
}

.alert-stream {
  display: flex;
  gap: var(--spacing-base);
  padding: var(--spacing-base) var(--spacing-panel);
  height: 100%;
  overflow-x: auto;
  overflow-y: hidden;
  scroll-behavior: smooth;
}

.alert-stream::-webkit-scrollbar {
  height: 4px;
}

.alert-stream::-webkit-scrollbar-track {
  background: rgba(255, 255, 255, 0.1);
}

.alert-stream::-webkit-scrollbar-thumb {
  background: var(--primary-color);
  border-radius: 2px;
}

.alert-item {
  flex: 0 0 300px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: var(--border-radius);
  padding: var(--spacing-base);
  border-left: 4px solid;
  transition: all 0.3s ease;
  cursor: default;
}

.alert-item.level-1 {
  border-left-color: #F44336;
  background: rgba(244, 67, 54, 0.1);
}

.alert-item.level-2 {
  border-left-color: #FF9800;
  background: rgba(255, 152, 0, 0.1);
}

.alert-item.level-3 {
  border-left-color: #FFC107;
  background: rgba(255, 193, 7, 0.1);
}

.alert-item.clickable {
  cursor: pointer;
}

.alert-item.clickable:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.alert-time {
  font-size: 11px;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  margin-bottom: 3px;
}

.alert-level-badge {
  display: inline-block;
  padding: 2px 6px;
  border-radius: 8px;
  font-size: 10px;
  font-weight: var(--font-weight-medium);
  margin-bottom: 5px;
}

.alert-level-badge.level-1 {
  background: #F44336;
  color: white;
}

.alert-level-badge.level-2 {
  background: #FF9800;
  color: white;
}

.alert-level-badge.level-3 {
  background: #FFC107;
  color: black;
}

.alert-source {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  margin-bottom: 5px;
}

.alert-message {
  font-size: 11px;
  color: var(--text-primary);
  line-height: 1.4;
  margin-bottom: 8px;
}

.alert-actions {
  display: flex;
  gap: 5px;
}

.action-btn {
  padding: 3px 8px;
  border: 1px solid;
  border-radius: 10px;
  font-size: 9px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn.link {
  border-color: var(--primary-color);
  color: var(--primary-color);
  background: transparent;
}

.action-btn.link:hover {
  background: var(--primary-color);
  color: white;
}

.action-btn.video {
  border-color: var(--status-normal);
  color: var(--status-normal);
  background: transparent;
}

.action-btn.video:hover {
  background: var(--status-normal);
  color: white;
}

.action-btn.details {
  border-color: var(--text-secondary);
  color: var(--text-secondary);
  background: transparent;
}

.action-btn.details:hover {
  background: var(--text-secondary);
  color: var(--bg-main);
}
</style>
