<template>
  <div class="notification-center">
    <!-- 推送通知列表 -->
    <div v-for="notification in visibleNotifications" 
         :key="notification.id" 
         class="notification-item" 
         :class="[notification.type, { 'entering': notification.entering, 'leaving': notification.leaving }]"
         @click="handleNotificationClick(notification)">
      
      <div class="notification-icon">
        {{ notification.icon }}
      </div>
      
      <div class="notification-content">
        <div class="notification-title">{{ notification.title }}</div>
        <div class="notification-message">{{ notification.message }}</div>
        <div class="notification-time">{{ notification.time }}</div>
      </div>
      
      <div class="notification-actions">
        <button class="action-btn primary" @click.stop="handleAction(notification, 'confirm')">
          {{ notification.confirmText || '确认' }}
        </button>
        <button class="action-btn secondary" @click.stop="handleAction(notification, 'dismiss')">
          {{ notification.dismissText || '忽略' }}
        </button>
      </div>
      
      <button class="close-btn" @click.stop="dismissNotification(notification.id)">
        ✕
      </button>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 通知数据
const notifications = ref([
  {
    id: 1,
    type: 'warning',
    icon: '💨',
    title: '大风预警通知',
    message: '预计明天风速超过6级，请各单位调整施工计划！',
    time: '刚刚',
    confirmText: '已知悉',
    dismissText: '稍后处理',
    priority: 'high',
    autoHide: false,
    actions: ['confirm', 'dismiss']
  },
  {
    id: 2,
    type: 'danger',
    icon: '🚨',
    title: '紧急安全提醒',
    message: '塔吊2号倾斜度接近预警值，请立即检查！',
    time: '2分钟前',
    confirmText: '立即处理',
    dismissText: '已处理',
    priority: 'critical',
    autoHide: false,
    actions: ['confirm', 'dismiss']
  },
  {
    id: 3,
    type: 'info',
    icon: '📢',
    title: '施工计划调整',
    message: '因天气原因，明日高空作业时间调整为9:00-15:00',
    time: '10分钟前',
    confirmText: '知道了',
    dismissText: '忽略',
    priority: 'normal',
    autoHide: true,
    autoHideDelay: 10000,
    actions: ['confirm', 'dismiss']
  }
])

const visibleNotifications = ref([])

// 显示通知的最大数量
const maxVisibleNotifications = 3

// 计算当前应该显示的通知
const updateVisibleNotifications = () => {
  // 按优先级和时间排序
  const sortedNotifications = notifications.value
    .filter(n => !n.dismissed)
    .sort((a, b) => {
      const priorityOrder = { critical: 3, high: 2, normal: 1 }
      if (priorityOrder[a.priority] !== priorityOrder[b.priority]) {
        return priorityOrder[b.priority] - priorityOrder[a.priority]
      }
      return b.id - a.id // 新的在前
    })
    .slice(0, maxVisibleNotifications)

  // 处理进入和离开动画
  const currentIds = visibleNotifications.value.map(n => n.id)
  const newIds = sortedNotifications.map(n => n.id)

  // 标记离开的通知
  visibleNotifications.value.forEach(notification => {
    if (!newIds.includes(notification.id)) {
      notification.leaving = true
    }
  })

  // 延迟移除离开的通知
  setTimeout(() => {
    visibleNotifications.value = visibleNotifications.value.filter(n => !n.leaving)
    
    // 添加新通知
    sortedNotifications.forEach(notification => {
      if (!currentIds.includes(notification.id)) {
        const newNotification = { ...notification, entering: true }
        visibleNotifications.value.push(newNotification)
        
        // 移除进入标记
        setTimeout(() => {
          newNotification.entering = false
        }, 100)
      }
    })
  }, 300)
}

// 处理通知点击
const handleNotificationClick = (notification) => {
  console.log('点击通知:', notification.title)
  // 可以在这里添加跳转到相关页面的逻辑
}

// 处理通知操作
const handleAction = (notification, action) => {
  console.log(`通知操作: ${action}`, notification.title)
  
  if (action === 'confirm') {
    // 确认操作
    if (notification.type === 'warning' && notification.title.includes('大风')) {
      // 可以触发相关的业务逻辑，比如打开施工计划调整页面
      console.log('打开施工计划调整页面')
    } else if (notification.type === 'danger' && notification.title.includes('塔吊')) {
      // 可以跳转到设备详情页面
      console.log('跳转到塔吊详情页面')
    }
  }
  
  // 移除通知
  dismissNotification(notification.id)
}

// 关闭通知
const dismissNotification = (id) => {
  const notification = notifications.value.find(n => n.id === id)
  if (notification) {
    notification.dismissed = true
    updateVisibleNotifications()
  }
}

// 添加新通知
const addNotification = (notification) => {
  const newNotification = {
    id: Date.now(),
    time: '刚刚',
    priority: 'normal',
    autoHide: true,
    autoHideDelay: 8000,
    ...notification
  }
  
  notifications.value.unshift(newNotification)
  updateVisibleNotifications()
  
  // 自动隐藏
  if (newNotification.autoHide) {
    setTimeout(() => {
      dismissNotification(newNotification.id)
    }, newNotification.autoHideDelay)
  }
}

// 模拟新通知
const simulateNotifications = () => {
  const mockNotifications = [
    {
      type: 'warning',
      icon: '⚠️',
      title: '设备维护提醒',
      message: '1号塔吊预计下周进行例行维护，请提前安排作业计划',
      priority: 'normal'
    },
    {
      type: 'info',
      icon: '📋',
      title: '安全检查通知',
      message: '今日下午15:00将进行安全专项检查，请各班组做好准备',
      priority: 'normal'
    },
    {
      type: 'danger',
      icon: '🌪️',
      title: '极端天气预警',
      message: '明日可能出现强对流天气，建议暂停所有室外作业',
      priority: 'critical'
    }
  ]
  
  // 随机添加通知
  setInterval(() => {
    if (Math.random() > 0.7) { // 30% 概率
      const randomNotification = mockNotifications[Math.floor(Math.random() * mockNotifications.length)]
      addNotification(randomNotification)
    }
  }, 15000) // 每15秒检查一次
}

// 暴露方法给父组件
defineExpose({
  addNotification,
  dismissNotification
})

onMounted(() => {
  updateVisibleNotifications()
  // 开启模拟通知（可选）
  // simulateNotifications()
})
</script>

<style scoped>
.notification-center {
  position: fixed;
  top: 80px;
  right: 20px;
  z-index: 1500;
  width: 400px;
  max-width: 90vw;
}

.notification-item {
  background: var(--bg-panel);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  padding: var(--spacing-panel);
  margin-bottom: var(--spacing-base);
  display: flex;
  align-items: flex-start;
  gap: var(--spacing-base);
  cursor: pointer;
  transition: all 0.3s ease;
  box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  position: relative;
  overflow: hidden;
}

.notification-item:hover {
  transform: translateX(-5px);
  box-shadow: 0 6px 25px rgba(0, 0, 0, 0.4);
}

.notification-item.entering {
  animation: slideInRight 0.3s ease-out;
}

.notification-item.leaving {
  animation: slideOutRight 0.3s ease-in;
}

.notification-item.warning {
  border-left: 4px solid var(--status-major);
}

.notification-item.danger {
  border-left: 4px solid var(--status-critical);
  animation: pulse 2s infinite;
}

.notification-item.info {
  border-left: 4px solid var(--primary-color);
}

.notification-icon {
  font-size: 24px;
  flex-shrink: 0;
  margin-top: 2px;
}

.notification-content {
  flex: 1;
  min-width: 0;
}

.notification-title {
  font-size: var(--font-size-h4);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 4px;
}

.notification-message {
  font-size: 13px;
  color: var(--text-secondary);
  line-height: 1.4;
  margin-bottom: 8px;
}

.notification-time {
  font-size: 11px;
  color: var(--text-tertiary);
}

.notification-actions {
  display: flex;
  flex-direction: column;
  gap: 4px;
  flex-shrink: 0;
}

.action-btn {
  padding: 4px 8px;
  border: none;
  border-radius: 4px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
  min-width: 60px;
}

.action-btn.primary {
  background: var(--primary-color);
  color: white;
}

.action-btn.primary:hover {
  background: var(--secondary-color);
}

.action-btn.secondary {
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border: 1px solid var(--border-color);
}

.action-btn.secondary:hover {
  background: rgba(255, 255, 255, 0.2);
}

.close-btn {
  position: absolute;
  top: 8px;
  right: 8px;
  width: 20px;
  height: 20px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
  border-radius: 50%;
  cursor: pointer;
  font-size: 12px;
  display: flex;
  align-items: center;
  justify-content: center;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--status-critical);
  color: white;
}

@keyframes slideInRight {
  from {
    transform: translateX(100%);
    opacity: 0;
  }
  to {
    transform: translateX(0);
    opacity: 1;
  }
}

@keyframes slideOutRight {
  from {
    transform: translateX(0);
    opacity: 1;
  }
  to {
    transform: translateX(100%);
    opacity: 0;
  }
}

@keyframes pulse {
  0% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
  50% {
    box-shadow: 0 4px 20px rgba(244, 67, 54, 0.4);
  }
  100% {
    box-shadow: 0 4px 20px rgba(0, 0, 0, 0.3);
  }
}

/* 响应式设计 */
@media (max-width: 768px) {
  .notification-center {
    top: 60px;
    right: 10px;
    left: 10px;
    width: auto;
  }
  
  .notification-item {
    padding: var(--spacing-base);
  }
  
  .notification-actions {
    flex-direction: row;
  }
}
</style>
