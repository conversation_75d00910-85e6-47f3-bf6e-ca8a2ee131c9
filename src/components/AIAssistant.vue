<template>
  <div class="ai-assistant-modal" v-if="isVisible" @click="closeModal">
    <div class="ai-container" @click.stop>
      <!-- AI助手头部 -->
      <div class="ai-header">
        <div class="ai-avatar">
          <div class="avatar-icon">🤖</div>
          <div class="avatar-status" :class="{ active: isListening, thinking: isThinking }"></div>
        </div>
        <div class="ai-info">
          <div class="ai-name">工地助手</div>
          <div class="ai-status-text">{{ statusText }}</div>
          <div class="ai-role">
            <select v-model="currentRole" @change="onRoleChange" class="role-selector">
              <option value="PROJECT_MANAGER">项目经理</option>
              <option value="SAFETY_SUPERVISOR">安全总监</option>
              <option value="CONSTRUCTION_LEADER">施工队长</option>
              <option value="EQUIPMENT_OPERATOR">设备操作员</option>
              <option value="SAFETY_OFFICER">安全员</option>
            </select>
          </div>
        </div>
        <div class="ai-controls">
          <button class="control-btn" :class="{ active: voiceMode }" @click="toggleVoiceMode" title="切换输入模式">
            {{ voiceMode ? '🎤' : '⌨️' }}
          </button>
          <button class="control-btn" :class="{ active: voiceEnabled }" @click="toggleVoiceOutput" title="语音播报">
            {{ voiceEnabled ? '🔊' : '🔇' }}
          </button>
          <button class="control-btn" @click="closeModal" title="关闭">✕</button>
        </div>
      </div>

      <!-- 对话区域 -->
      <div class="conversation-area" ref="conversationArea">
        <div v-for="message in messages" :key="message.id" 
             class="message" :class="message.type">
          
          <div class="message-avatar">
            <span v-if="message.type === 'user'">👤</span>
            <span v-else>🤖</span>
          </div>
          
          <div class="message-content">
            <div class="message-text" v-html="formatMessage(message.text)"></div>
            <div class="message-time">{{ message.time }}</div>
            
            <!-- 数据卡片 -->
            <div v-if="message.data" class="message-data">
              <div v-for="item in message.data" :key="item.label" class="data-item">
                <span class="data-label">{{ item.label }}:</span>
                <span class="data-value" :class="item.status">{{ item.value }}</span>
              </div>
            </div>
            
            <!-- 操作按钮 -->
            <div v-if="message.actions" class="message-actions">
              <button v-for="action in message.actions" :key="action.text"
                      class="action-btn" @click="executeAction(action)">
                {{ action.text }}
              </button>
            </div>
          </div>
        </div>
        
        <!-- 思考中指示器 -->
        <div v-if="isThinking" class="thinking-indicator">
          <div class="message-avatar">
            <span>🤖</span>
          </div>
          <div class="thinking-dots">
            <span></span><span></span><span></span>
          </div>
        </div>
      </div>

      <!-- 输入区域 -->
      <div class="input-area">
        <div v-if="voiceMode" class="voice-input">
          <button class="voice-btn" 
                  :class="{ listening: isListening, disabled: isThinking }"
                  @click="toggleListening"
                  :disabled="isThinking">
            <span class="voice-icon">{{ isListening ? '🔴' : '🎤' }}</span>
            <span class="voice-text">{{ isListening ? '正在聆听...' : '点击说话' }}</span>
          </button>
          <div v-if="voiceText" class="voice-preview">{{ voiceText }}</div>
        </div>
        
        <div v-else class="text-input">
          <input type="text" 
                 v-model="inputText" 
                 @keyup.enter="sendMessage"
                 :disabled="isThinking"
                 placeholder="请输入您的问题..."
                 class="text-field">
          <button class="send-btn" 
                  @click="sendMessage" 
                  :disabled="!inputText.trim() || isThinking">
            发送
          </button>
        </div>
      </div>

      <!-- 快捷问题 -->
      <div class="quick-questions" v-if="messages.length === 0">
        <div class="quick-title">常用问题</div>
        <div class="quick-buttons">
          <button v-for="question in quickQuestions" :key="question"
                  class="quick-btn" @click="askQuestion(question)">
            {{ question }}
          </button>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted, nextTick, watch } from 'vue'
import { aiEngine } from '../utils/aiAnalysisEngine.js'
import { roleResponseSystem } from '../utils/roleBasedResponses.js'

// Props
const props = defineProps({
  visible: {
    type: Boolean,
    default: false
  }
})

// Emits
const emit = defineEmits(['close', 'action'])

// 响应式数据
const isVisible = ref(props.visible)
const voiceMode = ref(false)
const isListening = ref(false)
const isThinking = ref(false)
const inputText = ref('')
const voiceText = ref('')
const messages = ref([])
const conversationArea = ref(null)
const currentRole = ref('PROJECT_MANAGER')

// 语音识别和合成相关
let recognition = null
let speechSynthesis = null
const voiceEnabled = ref(true)

// 状态文本
const statusText = computed(() => {
  if (isThinking.value) return '正在分析...'
  if (isListening.value) return '正在聆听...'
  return '在线 - 随时为您服务'
})

// 快捷问题 - 根据角色动态变化
const quickQuestions = computed(() => {
  const roleQuestions = {
    PROJECT_MANAGER: [
      '报告今天的主要施工风险',
      '明天连续浇筑作业安全吗',
      '分析进度与安全的平衡',
      '查看项目整体状况',
      '评估资源配置情况'
    ],
    SAFETY_SUPERVISOR: [
      '检查所有安全风险点',
      '调出最新告警事件',
      '分析事故隐患趋势',
      '查看应急预案状态',
      '检查人员安全行为'
    ],
    CONSTRUCTION_LEADER: [
      '今日班前安全交底',
      '查看作业区域风险',
      '确认人员到位情况',
      '检查材料堆放安全',
      '获取作业指导建议'
    ],
    EQUIPMENT_OPERATOR: [
      '检查设备实时状态',
      '查看操作安全提醒',
      '获取天气影响分析',
      '确认设备维保计划',
      '查看操作规程要点'
    ],
    SAFETY_OFFICER: [
      '巡检安全要点提醒',
      '查看违章行为记录',
      '分析安全培训需求',
      '检查防护用品使用',
      '获取安全检查清单'
    ]
  }

  return roleQuestions[currentRole.value] || roleQuestions.PROJECT_MANAGER
})

// 监听visible属性变化
watch(() => props.visible, (newVal) => {
  isVisible.value = newVal
  if (newVal && messages.value.length === 0) {
    // 初始化欢迎消息
    addMessage('assistant', '您好！我是工地助手，专为电厂施工安全监控而设计。我可以帮您分析风险、预测问题、提供决策支持。请问有什么可以为您服务的？')
  }
})

// 方法
const closeModal = () => {
  isVisible.value = false
  emit('close')
}

const toggleVoiceMode = () => {
  voiceMode.value = !voiceMode.value
  if (voiceMode.value) {
    initSpeechRecognition()
  }
}

const toggleVoiceOutput = () => {
  voiceEnabled.value = !voiceEnabled.value

  // 如果关闭语音输出，停止当前播放
  if (!voiceEnabled.value && 'speechSynthesis' in window) {
    window.speechSynthesis.cancel()
  }

  // 添加提示消息
  const statusText = voiceEnabled.value ? '语音播报已开启' : '语音播报已关闭'
  addMessage('assistant', statusText)
}

const toggleListening = () => {
  if (isListening.value) {
    stopListening()
  } else {
    startListening()
  }
}

const startListening = () => {
  if (!recognition) return
  
  isListening.value = true
  voiceText.value = ''
  recognition.start()
}

const stopListening = () => {
  if (!recognition) return
  
  isListening.value = false
  recognition.stop()
}

const initSpeechRecognition = () => {
  if ('webkitSpeechRecognition' in window || 'SpeechRecognition' in window) {
    const SpeechRecognition = window.SpeechRecognition || window.webkitSpeechRecognition
    recognition = new SpeechRecognition()
    
    recognition.continuous = false
    recognition.interimResults = true
    recognition.lang = 'zh-CN'
    
    recognition.onresult = (event) => {
      let finalTranscript = ''
      let interimTranscript = ''
      
      for (let i = event.resultIndex; i < event.results.length; i++) {
        const transcript = event.results[i][0].transcript
        if (event.results[i].isFinal) {
          finalTranscript += transcript
        } else {
          interimTranscript += transcript
        }
      }
      
      voiceText.value = finalTranscript || interimTranscript
      
      if (finalTranscript) {
        inputText.value = finalTranscript
        sendMessage()
      }
    }
    
    recognition.onend = () => {
      isListening.value = false
    }
    
    recognition.onerror = (event) => {
      console.error('语音识别错误:', event.error)
      isListening.value = false
    }
  }
}

const addMessage = (type, text, data = null, actions = null) => {
  const message = {
    id: Date.now(),
    type,
    text,
    data,
    actions,
    time: new Date().toLocaleTimeString('zh-CN', { hour12: false })
  }

  messages.value.push(message)

  // 如果是AI回复且启用了语音，则朗读消息
  if (type === 'assistant' && voiceEnabled.value) {
    speakMessage(text)
  }

  nextTick(() => {
    if (conversationArea.value) {
      conversationArea.value.scrollTop = conversationArea.value.scrollHeight
    }
  })
}

// 语音合成功能
const speakMessage = (text) => {
  if ('speechSynthesis' in window) {
    // 停止当前播放
    window.speechSynthesis.cancel()

    // 清理文本中的markdown标记
    const cleanText = text
      .replace(/\*\*(.*?)\*\*/g, '$1')
      .replace(/\*(.*?)\*/g, '$1')
      .replace(/\n/g, '。')
      .replace(/[🚨⚠️✅🛑🏃‍♂️📞🚫📹👥📊🌤️🏗️]/g, '')

    const utterance = new SpeechSynthesisUtterance(cleanText)
    utterance.lang = 'zh-CN'
    utterance.rate = 0.9
    utterance.pitch = 1.0
    utterance.volume = 0.8

    window.speechSynthesis.speak(utterance)
  }
}

const sendMessage = () => {
  if (!inputText.value.trim() || isThinking.value) return
  
  const userMessage = inputText.value.trim()
  addMessage('user', userMessage)
  
  inputText.value = ''
  voiceText.value = ''
  
  // 处理AI回复
  processAIResponse(userMessage)
}

const askQuestion = (question) => {
  inputText.value = question
  sendMessage()
}

const formatMessage = (text) => {
  // 简单的文本格式化，支持换行和高亮
  return text.replace(/\n/g, '<br>')
             .replace(/\*\*(.*?)\*\*/g, '<strong>$1</strong>')
             .replace(/\*(.*?)\*/g, '<em>$1</em>')
}

const executeAction = (action) => {
  emit('action', action)
  addMessage('assistant', `正在执行：${action.text}`)
}

const onRoleChange = () => {
  roleResponseSystem.setRole(currentRole.value)

  // 添加角色切换消息
  const roleNames = {
    PROJECT_MANAGER: '项目经理',
    SAFETY_SUPERVISOR: '安全总监',
    CONSTRUCTION_LEADER: '施工队长',
    EQUIPMENT_OPERATOR: '设备操作员',
    SAFETY_OFFICER: '安全员'
  }

  addMessage('assistant', `您好！我已切换为${roleNames[currentRole.value]}专用模式。我将为您提供针对性的专业建议和决策支持。有什么可以为您服务的？`)
}

// AI响应处理（模拟智能分析）
const processAIResponse = async (userMessage) => {
  isThinking.value = true
  
  // 模拟思考时间
  await new Promise(resolve => setTimeout(resolve, 1500))
  
  const response = generateAIResponse(userMessage)
  
  isThinking.value = false
  addMessage('assistant', response.text, response.data, response.actions)
}

// 生成AI响应（集成智能分析引擎和角色化响应）
const generateAIResponse = (message) => {
  const lowerMessage = message.toLowerCase()

  // 获取当前系统数据（模拟从各个组件获取）
  const systemData = getCurrentSystemData()

  // 检查是否是特定角色的专用场景
  const roleSpecificResponse = checkRoleSpecificScenarios(message, systemData)
  if (roleSpecificResponse) {
    return roleSpecificResponse
  }

  // 风险报告 - 使用AI引擎进行综合分析
  if (lowerMessage.includes('风险') || lowerMessage.includes('报告')) {
    const riskAnalysis = aiEngine.analyzeComprehensiveRisk(systemData)

    let riskText = '**今日综合风险分析报告：**\n\n'

    // 添加各类风险分析
    Object.entries(riskAnalysis.categories).forEach(([category, risk]) => {
      const categoryNames = {
        weather: '气象风险',
        structural: '结构风险',
        personnel: '人员风险',
        seismic: '地震风险'
      }
      riskText += `**${categoryNames[category]}**：${risk.level === 'high' ? '高' : risk.level === 'medium' ? '中' : '低'}风险`
      if (risk.factors && risk.factors.length > 0) {
        riskText += ` - ${risk.factors[0]}`
      }
      riskText += '\n'
    })

    riskText += `\n**综合风险等级：${riskAnalysis.overall.level === 'high' ? '高' : riskAnalysis.overall.level === 'medium' ? '中' : '低'}**`

    if (riskAnalysis.recommendations && riskAnalysis.recommendations.length > 0) {
      riskText += '\n\n**建议措施：**\n'
      riskAnalysis.recommendations.forEach((rec, index) => {
        riskText += `${index + 1}. ${rec}\n`
      })
    }

    return {
      text: riskText,
      data: Object.entries(riskAnalysis.categories).map(([key, risk]) => ({
        label: key === 'weather' ? '气象风险' : key === 'structural' ? '结构风险' : key === 'personnel' ? '人员风险' : '地震风险',
        value: risk.level === 'high' ? '高' : risk.level === 'medium' ? '中' : '低',
        status: risk.level === 'high' ? 'danger' : risk.level === 'medium' ? 'warning' : 'normal'
      })),
      actions: [
        { text: '查看详细数据', type: 'showDetails' },
        { text: '发送预警通知', type: 'sendAlert' },
        { text: '生成风险报告', type: 'generateReport' }
      ]
    }
  }
  
  // 塔吊状态 - 使用AI引擎进行设备分析
  if (lowerMessage.includes('塔吊') || lowerMessage.includes('设备')) {
    const equipmentAnalysis = aiEngine.predictiveMaintenance(
      { cranes: systemData.cranes, scaffolds: systemData.scaffolds },
      { windSpeed: systemData.weather.windSpeed, temperature: systemData.weather.temperature }
    )

    let equipmentText = '**塔吊设备智能分析：**\n\n'

    systemData.cranes.forEach(crane => {
      const prediction = equipmentAnalysis.predictions[crane.id]
      equipmentText += `**${crane.name}**：${crane.status === 'normal' ? '运行正常' : crane.status === 'warning' ? '预警状态' : '报警状态'}`
      equipmentText += `，倾斜度${crane.tilt.toFixed(1)}°`
      if (prediction) {
        equipmentText += `，风险评分${prediction.riskScore.toFixed(0)}`
      }
      equipmentText += '\n'
    })

    equipmentText += `\n当前环境风速${systemData.weather.windSpeed}m/s`

    if (equipmentAnalysis.criticalAlerts && equipmentAnalysis.criticalAlerts.length > 0) {
      equipmentText += '\n\n**关键预警：**\n'
      equipmentAnalysis.criticalAlerts.forEach(alert => {
        equipmentText += `• ${alert}\n`
      })
    }

    return {
      text: equipmentText,
      data: systemData.cranes.map(crane => ({
        label: crane.name,
        value: crane.status === 'normal' ? '正常' : crane.status === 'warning' ? '预警' : '报警',
        status: crane.status
      })),
      actions: [
        { text: '定位到3D模型', type: 'locateEquipment' },
        { text: '查看预测维护', type: 'showMaintenance' },
        { text: '生成设备报告', type: 'generateEquipmentReport' }
      ]
    }
  }
  
  // 气象预报
  if (lowerMessage.includes('气象') || lowerMessage.includes('天气')) {
    return {
      text: '**未来24小时气象预报：**\n\n**今日下午**：多云转阴，风力7级，不适宜高空作业\n**今晚**：小雨，风力减弱至4级\n**明日上午**：晴，风力3级，适宜施工\n\n**建议**：下午暂停塔吊和高空作业，明日恢复正常施工。',
      data: [
        { label: '当前风速', value: '28km/h', status: 'warning' },
        { label: '预计最大风速', value: '45km/h', status: 'danger' },
        { label: '降雨概率', value: '80%', status: 'warning' }
      ],
      actions: [
        { text: '发送停工通知', type: 'sendStopWork' },
        { text: '查看详细预报', type: 'showWeatherDetail' }
      ]
    }
  }
  
  // 默认回复
  return {
    text: '我理解您的问题。作为工地助手，我可以帮您分析施工风险、监控设备状态、预测天气影响、管理人员安全等。请告诉我您需要了解哪个方面的具体信息？',
    actions: [
      { text: '风险分析', type: 'riskAnalysis' },
      { text: '设备监控', type: 'equipmentMonitor' },
      { text: '人员管理', type: 'personnelManagement' }
    ]
  }
}

// 获取当前系统数据（模拟从各个组件获取实时数据）
const getCurrentSystemData = () => {
  return {
    weather: {
      windSpeed: 28,
      temperature: 25,
      humidity: 65,
      visibility: 12,
      forecast: [
        { time: '14:00', windSpeed: 30 },
        { time: '16:00', windSpeed: 35 },
        { time: '18:00', windSpeed: 25 }
      ]
    },
    cranes: [
      { id: 1, name: '1号塔吊', status: 'normal', tilt: 0.8, load: 45 },
      { id: 2, name: '2号塔吊', status: 'warning', tilt: 1.2, load: 78 },
      { id: 3, name: '3号塔吊', status: 'normal', tilt: 0.5, load: 32 }
    ],
    scaffolds: [
      {
        id: 1,
        name: '高支模A区',
        status: 'normal',
        points: [
          { id: 1, type: 'tilt', value: 0.3, status: 'normal' },
          { id: 2, type: 'settlement', value: 2.5, status: 'warning' }
        ]
      }
    ],
    personnel: {
      total: 63,
      byType: [
        { name: '施工人员', count: 28 },
        { name: '技术人员', count: 15 },
        { name: '安全员', count: 8 },
        { name: '管理人员', count: 12 }
      ]
    },
    seismic: {
      currentPGA: 0.023,
      status: 'normal'
    },
    alerts: [
      { level: 'level-1', count: 2 },
      { level: 'level-2', count: 3 },
      { level: 'level-3', count: 1 }
    ]
  }
}

// 检查角色特定场景
const checkRoleSpecificScenarios = (message, systemData) => {
  const lowerMessage = message.toLowerCase()

  // 项目经理专用场景
  if (currentRole.value === 'PROJECT_MANAGER') {
    if (lowerMessage.includes('晨会') || lowerMessage.includes('日报') || lowerMessage.includes('今天') && lowerMessage.includes('风险')) {
      return roleResponseSystem.generateRoleBasedResponse('DAILY_BRIEFING', systemData, { time: 'morning' })
    }

    if (lowerMessage.includes('浇筑') && (lowerMessage.includes('安全') || lowerMessage.includes('可行'))) {
      return roleResponseSystem.generateRoleBasedResponse('CONSTRUCTION_FEASIBILITY', systemData, { activity: 'concrete' })
    }
  }

  // 安全总监专用场景
  if (currentRole.value === 'SAFETY_SUPERVISOR') {
    if (lowerMessage.includes('调出') && lowerMessage.includes('录像')) {
      return roleResponseSystem.generateRoleBasedResponse('INCIDENT_INVESTIGATION', systemData, { incident: 'intrusion' })
    }
  }

  // 施工队长专用场景
  if (currentRole.value === 'CONSTRUCTION_LEADER') {
    if (lowerMessage.includes('班前') || lowerMessage.includes('交底') || lowerMessage.includes('注意')) {
      return roleResponseSystem.generateRoleBasedResponse('PRE_WORK_BRIEFING', systemData, { area: 'scaffold' })
    }
  }

  // 设备操作员专用场景
  if (currentRole.value === 'EQUIPMENT_OPERATOR') {
    if (lowerMessage.includes('状态') || lowerMessage.includes('安全') || lowerMessage.includes('塔吊')) {
      return roleResponseSystem.generateRoleBasedResponse('REAL_TIME_STATUS', systemData, { equipment: 'crane' })
    }
  }

  return null
}

onMounted(() => {
  if (voiceMode.value) {
    initSpeechRecognition()
  }
})

onUnmounted(() => {
  if (recognition) {
    recognition.stop()
  }
})
</script>

<style scoped>
.ai-assistant-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  width: 100vw;
  height: 100vh;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 9999;
  backdrop-filter: blur(5px);
  animation: fadeIn 0.3s ease-out;
}

.ai-container {
  width: 90%;
  max-width: 800px;
  height: 80%;
  max-height: 600px;
  min-height: 500px;
  background: var(--bg-panel);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  box-shadow: 0 20px 60px rgba(0, 0, 0, 0.5);
  animation: slideIn 0.3s ease-out;
}

@keyframes fadeIn {
  from {
    opacity: 0;
  }
  to {
    opacity: 1;
  }
}

@keyframes slideIn {
  from {
    opacity: 0;
    transform: translateY(-50px) scale(0.9);
  }
  to {
    opacity: 1;
    transform: translateY(0) scale(1);
  }
}

.ai-header {
  display: flex;
  align-items: center;
  padding: var(--spacing-panel);
  border-bottom: 1px solid var(--border-color);
  background: rgba(64, 196, 255, 0.1);
}

.ai-avatar {
  position: relative;
  margin-right: var(--spacing-panel);
}

.avatar-icon {
  width: 48px;
  height: 48px;
  background: var(--primary-color);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 24px;
  color: white;
}

.avatar-status {
  position: absolute;
  bottom: 2px;
  right: 2px;
  width: 12px;
  height: 12px;
  border-radius: 50%;
  background: #4CAF50;
  border: 2px solid white;
}

.avatar-status.active {
  background: #FF4444;
  animation: pulse 1s infinite;
}

.avatar-status.thinking {
  background: #FFA500;
  animation: pulse 2s infinite;
}

.ai-info {
  flex: 1;
}

.ai-name {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.ai-status-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.ai-role {
  margin-top: 5px;
}

.role-selector {
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 4px;
  color: var(--text-primary);
  font-size: 11px;
  cursor: pointer;
}

.role-selector:focus {
  outline: none;
  border-color: var(--primary-color);
}

.ai-controls {
  display: flex;
  gap: var(--spacing-base);
}

.control-btn {
  width: 36px;
  height: 36px;
  border: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 50%;
  display: flex;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
  font-size: 14px;
  color: var(--text-secondary);
}

.control-btn:hover,
.control-btn.active {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

.conversation-area {
  flex: 1;
  padding: var(--spacing-panel);
  overflow-y: auto;
  display: flex;
  flex-direction: column;
  gap: var(--spacing-panel);
}

.message {
  display: flex;
  gap: var(--spacing-base);
  align-items: flex-start;
}

.message.user {
  flex-direction: row-reverse;
}

.message-avatar {
  width: 32px;
  height: 32px;
  border-radius: 50%;
  background: rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 16px;
  flex-shrink: 0;
}

.message.user .message-avatar {
  background: var(--primary-color);
}

.message-content {
  flex: 1;
  max-width: 70%;
}

.message.user .message-content {
  text-align: right;
}

.message-text {
  background: rgba(255, 255, 255, 0.1);
  padding: var(--spacing-base);
  border-radius: 12px;
  color: var(--text-primary);
  line-height: 1.5;
  margin-bottom: 5px;
}

.message.user .message-text {
  background: var(--primary-color);
  color: white;
}

.message-time {
  font-size: 11px;
  color: var(--text-secondary);
}

.message-data {
  margin-top: var(--spacing-base);
  padding: var(--spacing-base);
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border-left: 3px solid var(--primary-color);
}

.data-item {
  display: flex;
  justify-content: space-between;
  margin-bottom: 5px;
  font-size: 12px;
}

.data-label {
  color: var(--text-secondary);
}

.data-value {
  font-weight: var(--font-weight-medium);
}

.data-value.normal { color: var(--status-normal); }
.data-value.warning { color: var(--status-major); }
.data-value.danger { color: var(--status-critical); }

.message-actions {
  margin-top: var(--spacing-base);
  display: flex;
  gap: 5px;
  flex-wrap: wrap;
}

.action-btn {
  padding: 4px 8px;
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 12px;
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.action-btn:hover {
  background: var(--secondary-color);
  transform: translateY(-1px);
}

.thinking-indicator {
  display: flex;
  gap: var(--spacing-base);
  align-items: center;
}

.thinking-dots {
  display: flex;
  gap: 4px;
}

.thinking-dots span {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: var(--primary-color);
  animation: thinking 1.4s infinite ease-in-out;
}

.thinking-dots span:nth-child(1) { animation-delay: -0.32s; }
.thinking-dots span:nth-child(2) { animation-delay: -0.16s; }

@keyframes thinking {
  0%, 80%, 100% {
    transform: scale(0);
    opacity: 0.5;
  }
  40% {
    transform: scale(1);
    opacity: 1;
  }
}

.input-area {
  padding: var(--spacing-panel);
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
}

.voice-input {
  text-align: center;
}

.voice-btn {
  display: flex;
  flex-direction: column;
  align-items: center;
  gap: 5px;
  padding: var(--spacing-panel);
  background: rgba(255, 255, 255, 0.1);
  border: 2px solid var(--border-color);
  border-radius: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
  width: 100%;
}

.voice-btn:hover:not(.disabled) {
  background: rgba(64, 196, 255, 0.2);
  border-color: var(--primary-color);
}

.voice-btn.listening {
  background: rgba(255, 68, 68, 0.2);
  border-color: #FF4444;
}

.voice-btn.disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.voice-icon {
  font-size: 24px;
}

.voice-text {
  font-size: 12px;
  color: var(--text-secondary);
}

.voice-preview {
  margin-top: var(--spacing-base);
  padding: var(--spacing-base);
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
  color: var(--text-primary);
  font-style: italic;
}

.text-input {
  display: flex;
  gap: var(--spacing-base);
}

.text-field {
  flex: 1;
  padding: var(--spacing-base);
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 8px;
  color: var(--text-primary);
  font-size: 14px;
}

.text-field:focus {
  outline: none;
  border-color: var(--primary-color);
}

.text-field::placeholder {
  color: var(--text-secondary);
}

.send-btn {
  padding: var(--spacing-base) var(--spacing-panel);
  background: var(--primary-color);
  color: white;
  border: none;
  border-radius: 8px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.send-btn:hover:not(:disabled) {
  background: var(--secondary-color);
}

.send-btn:disabled {
  opacity: 0.5;
  cursor: not-allowed;
}

.quick-questions {
  padding: var(--spacing-panel);
  border-top: 1px solid var(--border-color);
  background: rgba(255, 255, 255, 0.02);
}

.quick-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: var(--spacing-base);
}

.quick-buttons {
  display: flex;
  flex-wrap: wrap;
  gap: 8px;
}

.quick-btn {
  padding: 6px 12px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid var(--border-color);
  border-radius: 16px;
  color: var(--text-primary);
  font-size: 12px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.quick-btn:hover {
  background: var(--primary-color);
  color: white;
  border-color: var(--primary-color);
}

@keyframes pulse {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.1);
  }
  100% {
    transform: scale(1);
  }
}
</style>
