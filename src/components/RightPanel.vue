<template>
  <div class="right-panel">

    <!-- 人员与门禁 -->
    <div class="data-module glass-effect">
      <div class="module-title">
        <span class="module-icon">👥</span>
        人员与门禁
      </div>
      <div class="module-content">
        <!-- 人员统计 -->
        <div class="personnel-stats">
          <div class="total-personnel">
            <div class="personnel-number">{{ totalPersonnel }}</div>
            <div class="personnel-label">场内总人数</div>
          </div>

          <div class="personnel-breakdown">
            <div v-for="type in personnelTypes" :key="type.name" class="personnel-type">
              <div class="type-icon">{{ type.icon }}</div>
              <div class="type-info">
                <div class="type-name">{{ type.name }}</div>
                <div class="type-count">{{ type.count }}人</div>
              </div>
            </div>
          </div>
        </div>

        <!-- 门禁记录 -->
        <div class="access-records">
          <div class="records-header">
            <span class="header-title">实时门禁记录</span>
            <span class="header-time">最近更新: {{ lastAccessUpdate }}</span>
          </div>

          <div class="records-list">
            <div v-for="record in accessRecords.slice(0, 4)" :key="record.id"
                 class="access-record" :class="record.type">
              <div class="record-time">{{ record.time }}</div>
              <div class="record-person">{{ record.person }}</div>
              <div class="record-action">{{ record.action }}</div>
              <div class="record-location">{{ record.location }}</div>
            </div>
          </div>
        </div>

        <!-- 异常告警 -->
        <div class="access-alerts">
          <div class="alerts-header">
            <span class="alerts-title">异常告警</span>
            <span class="alerts-count" :class="{ danger: accessAlerts.length > 0 }">
              {{ accessAlerts.length }}条
            </span>
          </div>

          <div class="alerts-list" v-if="accessAlerts.length > 0">
            <div v-for="alert in accessAlerts" :key="alert.id"
                 class="access-alert" :class="alert.level">
              <div class="alert-icon">⚠️</div>
              <div class="alert-content">
                <div class="alert-message">{{ alert.message }}</div>
                <div class="alert-time">{{ alert.time }}</div>
              </div>
            </div>
          </div>

          <div v-else class="no-alerts">
            <span class="no-alerts-icon">✅</span>
            <span class="no-alerts-text">暂无异常</span>
          </div>
        </div>
      </div>
    </div>

    <!-- 安全风险等级监控 -->
    <div class="data-module glass-effect">
      <div class="module-title">
        <span class="module-icon">🛡️</span>
        安全风险等级监控
      </div>
      <div class="module-content">
        <div class="risk-cards">
          <!-- 重大风险卡片 -->
          <div class="risk-card critical" @click="viewRiskDetails('critical')">
            <div class="risk-header">
              <div class="risk-icon">🚨</div>
              <div class="risk-level">重大风险</div>
            </div>
            <div class="risk-count">{{ riskStats.critical }}</div>
            <div class="risk-action">点击查看全部</div>
          </div>

          <!-- 较大风险卡片 -->
          <div class="risk-card major" @click="viewRiskDetails('major')">
            <div class="risk-header">
              <div class="risk-icon">⚠️</div>
              <div class="risk-level">较大风险</div>
            </div>
            <div class="risk-count">{{ riskStats.major }}</div>
            <div class="risk-action">点击查看全部</div>
          </div>

          <!-- 一般风险卡片 -->
          <div class="risk-card moderate" @click="viewRiskDetails('moderate')">
            <div class="risk-header">
              <div class="risk-icon">⚡</div>
              <div class="risk-level">一般风险</div>
            </div>
            <div class="risk-count">{{ riskStats.moderate }}</div>
            <div class="risk-action">点击查看全部</div>
          </div>

          <!-- 较小风险卡片 -->
          <div class="risk-card minor" @click="viewRiskDetails('minor')">
            <div class="risk-header">
              <div class="risk-icon">💡</div>
              <div class="risk-level">较小风险</div>
            </div>
            <div class="risk-count">{{ riskStats.minor }}</div>
            <div class="risk-action">点击查看全部</div>
          </div>
        </div>

        <!-- 风险趋势图 -->
        <div class="risk-trend">
          <div class="trend-title">24小时风险趋势</div>
          <div class="trend-chart">
            <svg viewBox="0 0 300 60" class="trend-svg">
              <defs>
                <linearGradient id="riskGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                  <stop offset="0%" style="stop-color:var(--status-critical);stop-opacity:0.3" />
                  <stop offset="100%" style="stop-color:var(--status-critical);stop-opacity:0" />
                </linearGradient>
              </defs>
              <!-- 趋势线 -->
              <path d="M 0 40 L 50 35 L 100 30 L 150 25 L 200 30 L 250 35 L 300 40"
                    stroke="var(--status-critical)"
                    stroke-width="2"
                    fill="none"/>
              <path d="M 0 60 L 0 40 L 50 35 L 100 30 L 150 25 L 200 30 L 250 35 L 300 40 L 300 60 Z"
                    fill="url(#riskGradient)"/>
            </svg>
          </div>
        </div>
      </div>
    </div>
  </div>

  <!-- 设备详情弹窗 -->
  <div v-if="showDetailModal" class="detail-modal" @click="closeDetailModal">
    <div class="detail-container" @click.stop>
      <div class="detail-header">
        <h3>{{ selectedEquipment.name }} - 详细信息</h3>
        <button class="close-btn" @click="closeDetailModal">✕</button>
      </div>

      <div class="detail-content">
        <!-- 塔吊详情视图 -->
        <div v-if="selectedEquipmentType === 'crane'" class="crane-detail">
          <div class="detail-left">
            <div class="section-title">实时数据与控制</div>

            <!-- 塔体倾斜 -->
            <div class="detail-section">
              <h4>塔体倾斜</h4>
              <div class="tilt-display">
                <div class="tilt-value">{{ selectedEquipment.tilt.toFixed(2) }}°</div>
                <div class="tilt-direction">倾斜方向: {{ selectedEquipment.tiltDirection || 'SE' }}</div>
                <div class="tilt-diagram">
                  <!-- 简化的倾斜示意图 -->
                  <svg viewBox="0 0 100 100" class="tilt-svg">
                    <line x1="50" y1="90" x2="50" y2="10" stroke="#666" stroke-width="2"/>
                    <line x1="50" y1="90"
                          :x2="50 + selectedEquipment.tilt * 10"
                          :y2="10"
                          stroke="var(--primary-color)"
                          stroke-width="3"/>
                    <text x="55" y="50" fill="var(--text-primary)" font-size="8">
                      {{ selectedEquipment.tilt.toFixed(1) }}°
                    </text>
                  </svg>
                </div>
              </div>
            </div>

            <!-- 四项位移 -->
            <div class="detail-section">
              <h4>四项位移</h4>
              <div class="displacement-list">
                <div class="displacement-item">
                  <span>X轴位移:</span>
                  <span>{{ selectedEquipment.displacement?.x || '2.1' }}mm</span>
                </div>
                <div class="displacement-item">
                  <span>Y轴位移:</span>
                  <span>{{ selectedEquipment.displacement?.y || '1.8' }}mm</span>
                </div>
                <div class="displacement-item">
                  <span>Z轴位移:</span>
                  <span>{{ selectedEquipment.displacement?.z || '0.5' }}mm</span>
                </div>
                <div class="displacement-item">
                  <span>旋转位移:</span>
                  <span>{{ selectedEquipment.displacement?.rotation || '0.2' }}°</span>
                </div>
              </div>
            </div>

            <!-- 关联环境 -->
            <div class="detail-section">
              <h4>关联环境</h4>
              <div class="environment-data">
                <div class="env-item">
                  <span>高空风速:</span>
                  <span>{{ selectedEquipment.windSpeed }}km/h</span>
                </div>
                <div class="env-item">
                  <span>风向:</span>
                  <span>{{ selectedEquipment.windDirection || 'SE' }}</span>
                </div>
                <div class="env-item">
                  <span>温度:</span>
                  <span>{{ selectedEquipment.temperature || '25' }}°C</span>
                </div>
              </div>
            </div>

            <!-- 联动视频 -->
            <div class="detail-section">
              <h4>联动视频</h4>
              <div class="video-feeds">
                <div class="video-placeholder">
                  <div class="video-icon">📹</div>
                  <div class="video-label">塔吊主视角</div>
                </div>
                <div class="video-placeholder">
                  <div class="video-icon">📹</div>
                  <div class="video-label">塔吊侧视角</div>
                </div>
              </div>
            </div>
          </div>

          <div class="detail-right">
            <div class="section-title">历史趋势与分析</div>

            <!-- 24小时倾斜度变化曲线 -->
            <div class="detail-section">
              <h4>24小时倾斜度变化</h4>
              <div class="trend-chart">
                <svg viewBox="0 0 300 120" class="chart-svg">
                  <defs>
                    <linearGradient id="trendGradient" x1="0%" y1="0%" x2="0%" y2="100%">
                      <stop offset="0%" style="stop-color:var(--primary-color);stop-opacity:0.3" />
                      <stop offset="100%" style="stop-color:var(--primary-color);stop-opacity:0" />
                    </linearGradient>
                  </defs>
                  <!-- 简化的趋势线 -->
                  <path d="M 0 80 L 50 75 L 100 70 L 150 85 L 200 90 L 250 85 L 300 80"
                        stroke="var(--primary-color)"
                        stroke-width="2"
                        fill="none"/>
                  <path d="M 0 120 L 0 80 L 50 75 L 100 70 L 150 85 L 200 90 L 250 85 L 300 80 L 300 120 Z"
                        fill="url(#trendGradient)"/>
                </svg>
              </div>
            </div>

            <!-- 历史告警记录 -->
            <div class="detail-section">
              <h4>历史告警记录</h4>
              <div class="alert-history">
                <div class="alert-record">
                  <div class="alert-time">14:30</div>
                  <div class="alert-msg">倾斜度超过预警值</div>
                  <div class="alert-status resolved">已处理</div>
                </div>
                <div class="alert-record">
                  <div class="alert-time">12:15</div>
                  <div class="alert-msg">风速过大，建议停工</div>
                  <div class="alert-status resolved">已处理</div>
                </div>
              </div>
            </div>

            <!-- AI助手分析 -->
            <div class="detail-section">
              <h4>AI助手分析</h4>
              <div class="ai-analysis">
                <div class="ai-icon">🤖</div>
                <div class="ai-text">
                  数据显示，塔吊倾斜度在风力超过5级时有明显波动，建议在大风预警期间加强巡检。
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 高支模详情视图 -->
        <div v-else-if="selectedEquipmentType === 'scaffold'" class="scaffold-detail">
          <div class="detail-left">
            <div class="section-title">测点矩阵与实时数据</div>

            <!-- 测点矩阵 -->
            <div class="detail-section">
              <h4>测点布局矩阵</h4>
              <div class="point-matrix">
                <div v-for="point in selectedEquipment.points" :key="point.id"
                     class="matrix-point" :class="point.status"
                     @click="selectPoint(point)">
                  <div class="point-number">{{ point.id }}</div>
                  <div class="point-value">{{ formatPointValue(point.value, point.type) }}</div>
                </div>
              </div>
            </div>

            <!-- 实时数据列表 -->
            <div class="detail-section">
              <h4>实时监测数据</h4>
              <div class="realtime-data">
                <div v-for="point in selectedEquipment.points" :key="point.id"
                     class="data-row" :class="point.status">
                  <span class="point-id">{{ point.id }}#</span>
                  <span class="point-type">{{ getPointTypeText(point.type) }}</span>
                  <span class="point-value">{{ formatPointValue(point.value, point.type) }}</span>
                  <span class="point-status">{{ getStatusText(point.status) }}</span>
                </div>
              </div>
            </div>
          </div>

          <div class="detail-right">
            <div class="section-title">数据分析与追溯</div>

            <!-- 关键测点历史曲线 -->
            <div class="detail-section">
              <h4>关键测点历史曲线</h4>
              <div class="history-chart">
                <svg viewBox="0 0 300 120" class="chart-svg">
                  <!-- 简化的历史曲线 -->
                  <path d="M 0 100 L 50 95 L 100 90 L 150 85 L 200 80 L 250 75 L 300 70"
                        stroke="var(--status-major)"
                        stroke-width="2"
                        fill="none"/>
                  <path d="M 0 110 L 50 105 L 100 100 L 150 95 L 200 90 L 250 85 L 300 80"
                        stroke="var(--status-normal)"
                        stroke-width="2"
                        fill="none"/>
                </svg>
              </div>
            </div>

            <!-- 整体沉降云图 -->
            <div class="detail-section">
              <h4>整体沉降云图</h4>
              <div class="settlement-heatmap">
                <div class="heatmap-grid">
                  <div v-for="i in 12" :key="i"
                       class="heatmap-cell"
                       :style="{ backgroundColor: getHeatmapColor(i) }">
                  </div>
                </div>
                <div class="heatmap-legend">
                  <span>0mm</span>
                  <div class="legend-bar"></div>
                  <span>-5mm</span>
                </div>
              </div>
            </div>

            <!-- 关联事件 -->
            <div class="detail-section">
              <h4>关联事件</h4>
              <div class="related-events">
                <div class="event-item">
                  <div class="event-time">13:30</div>
                  <div class="event-desc">C区混凝土浇筑开始</div>
                  <div class="event-impact">沉降增加0.8mm</div>
                </div>
                <div class="event-item">
                  <div class="event-time">11:45</div>
                  <div class="event-desc">材料堆放调整</div>
                  <div class="event-impact">轴力减少2.1kN</div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'

// 塔吊数据
const cranes = ref([
  {
    id: 1,
    name: '1号塔吊',
    status: 'normal',
    tilt: 0.21,
    windSpeed: 25,
    windDirection: 'SE',
    temperature: 25,
    alertCount: 0,
    tiltDirection: 'SE',
    displacement: {
      x: 2.1,
      y: 1.8,
      z: 0.5,
      rotation: 0.2
    }
  },
  {
    id: 2,
    name: '2号塔吊',
    status: 'warning',
    tilt: 1.2,
    windSpeed: 28,
    windDirection: 'S',
    temperature: 26,
    alertCount: 2,
    tiltDirection: 'S',
    displacement: {
      x: 3.1,
      y: 2.8,
      z: 1.5,
      rotation: 1.2
    }
  }
])

// 高支模数据
const scaffolds = ref([
  {
    id: 1,
    name: 'C区高支模',
    status: 'warning',
    maxSettlement: -1.5,
    maxAxial: 35.2,
    abnormalPoints: 1,
    points: [
      { id: 1, type: 'tilt', value: 0.3, maxValue: 0.5, status: 'normal' },
      { id: 2, type: 'settlement', value: -1.5, maxValue: -2.1, status: 'warning' },
      { id: 3, type: 'axial', value: 35.2, maxValue: 38.5, status: 'normal' },
      { id: 4, type: 'tilt', value: 0.8, maxValue: 1.1, status: 'normal' },
      { id: 5, type: 'settlement', value: -2.8, maxValue: -3.2, status: 'danger' },
      { id: 6, type: 'axial', value: 42.1, maxValue: 45.3, status: 'warning' }
    ]
  }
])

// 人员数据
const totalPersonnel = ref(63)
const personnelTypes = ref([
  { name: '施工人员', icon: '👷', count: 28 },
  { name: '技术人员', icon: '👨‍💼', count: 15 },
  { name: '安全员', icon: '🦺', count: 8 },
  { name: '管理人员', icon: '👔', count: 12 }
])

const lastAccessUpdate = ref('')

// 门禁记录
const accessRecords = ref([
  { id: 1, time: '14:32', person: '张三', action: '进入', location: '主入口', type: 'normal' },
  { id: 2, time: '14:30', person: '李四', action: '离开', location: '侧门', type: 'normal' },
  { id: 3, time: '14:28', person: '王五', action: '进入', location: '主入口', type: 'normal' },
  { id: 4, time: '14:25', person: '赵六', action: '进入', location: '材料门', type: 'normal' }
])

// 异常告警
const accessAlerts = ref([
  {
    id: 1,
    message: '南门区域发生非法闯入',
    time: '14:20',
    level: 'danger'
  }
])

// 设备详情弹窗
const showDetailModal = ref(false)
const selectedEquipment = ref(null)
const selectedEquipmentType = ref('')

// 安全风险统计数据
const riskStats = ref({
  critical: 2,    // 重大风险
  major: 5,       // 较大风险
  moderate: 12,   // 一般风险
  minor: 8        // 较小风险
})

// 方法
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '预警',
    danger: '报警'
  }
  return statusMap[status] || '未知'
}

const getTiltStatus = (tilt) => {
  if (tilt >= 1.5) return 'danger'
  if (tilt >= 1.0) return 'warning'
  return 'normal'
}

const getSettlementStatus = (settlement) => {
  const absValue = Math.abs(settlement)
  if (absValue >= 3.0) return 'danger'
  if (absValue >= 2.0) return 'warning'
  return 'normal'
}

const getAxialStatus = (axial) => {
  if (axial >= 40) return 'warning'
  if (axial >= 45) return 'danger'
  return 'normal'
}

const getPointTypeText = (type) => {
  const typeMap = {
    tilt: '倾斜',
    settlement: '沉降',
    axial: '轴力'
  }
  return typeMap[type] || type
}

const formatPointValue = (value, type) => {
  if (type === 'tilt') return value.toFixed(1) + '°'
  if (type === 'settlement') return value.toFixed(1) + 'mm'
  if (type === 'axial') return value.toFixed(1) + 'kN'
  return value.toString()
}

// 设备详情相关方法
const showEquipmentDetails = (equipment, type) => {
  selectedEquipment.value = equipment
  selectedEquipmentType.value = type
  showDetailModal.value = true

  // 触发与3D模型的联动
  console.log(`显示${equipment.name}详情，类型：${type}`)
}

const closeDetailModal = () => {
  showDetailModal.value = false
  selectedEquipment.value = null
  selectedEquipmentType.value = ''
}

const selectPoint = (point) => {
  console.log('选中测点:', point)
}

const getHeatmapColor = (index) => {
  const colors = [
    '#4CAF50', '#8BC34A', '#CDDC39', '#FFEB3B',
    '#FFC107', '#FF9800', '#FF5722', '#F44336',
    '#E91E63', '#9C27B0', '#673AB7', '#3F51B5'
  ]
  return colors[index % colors.length]
}

// 查看风险详情
const viewRiskDetails = (riskLevel) => {
  const riskNames = {
    critical: '重大风险',
    major: '较大风险',
    moderate: '一般风险',
    minor: '较小风险'
  }

  console.log(`查看${riskNames[riskLevel]}详情，共${riskStats.value[riskLevel]}项`)

  // 这里可以打开风险详情弹窗或跳转到风险管理页面
  // 示例：显示风险列表
  alert(`${riskNames[riskLevel]}详情\n\n当前共有 ${riskStats.value[riskLevel]} 项${riskNames[riskLevel]}\n\n点击确定查看详细列表...`)
}

const updateTime = () => {
  const now = new Date()
  lastAccessUpdate.value = now.toLocaleTimeString('zh-CN', { hour12: false })
}

// 数据更新
let dataInterval = null

const updateData = () => {
  // 更新塔吊倾斜度
  cranes.value.forEach(crane => {
    // 生成新的倾斜度数据
    const baseTilt = crane.id === 1 ? 0.21 : (crane.id === 2 ? 1.2 : 0.5)
    crane.tilt = baseTilt + (Math.random() - 0.5) * 0.3

    // 更新风速
    crane.windSpeed = Math.floor(Math.random() * 10) + 20

    // 更新告警数量
    if (Math.random() > 0.9) {
      crane.alertCount = Math.floor(Math.random() * 3)
    }

    // 更新状态
    if (crane.tilt >= 1.5) crane.status = 'danger'
    else if (crane.tilt >= 1.0) crane.status = 'warning'
    else crane.status = 'normal'
  })

  // 更新高支模数据
  scaffolds.value.forEach(scaffold => {
    let abnormalCount = 0

    scaffold.points.forEach(point => {
      if (point.type === 'tilt') {
        point.value = Math.random() * 2
      } else if (point.type === 'settlement') {
        point.value = -(Math.random() * 4 + 0.5) // 负值表示沉降
      } else if (point.type === 'axial') {
        point.value = Math.floor(Math.random() * 20) + 30
      }

      // 更新状态
      const rand = Math.random()
      if (rand > 0.85) {
        point.status = 'danger'
        abnormalCount++
      } else if (rand > 0.7) {
        point.status = 'warning'
        abnormalCount++
      } else {
        point.status = 'normal'
      }
    })

    // 更新高支模整体数据
    scaffold.abnormalPoints = abnormalCount
    scaffold.maxSettlement = Math.min(...scaffold.points.filter(p => p.type === 'settlement').map(p => p.value))
    scaffold.maxAxial = Math.max(...scaffold.points.filter(p => p.type === 'axial').map(p => p.value))

    // 更新高支模状态
    if (abnormalCount > 1) scaffold.status = 'danger'
    else if (abnormalCount > 0) scaffold.status = 'warning'
    else scaffold.status = 'normal'
  })

  updateTime()
}

onMounted(() => {
  updateTime()
  dataInterval = setInterval(updateData, 3000)
})

onUnmounted(() => {
  if (dataInterval) {
    clearInterval(dataInterval)
  }
})
</script>

<style scoped>
.module-icon {
  margin-right: 8px;
  font-size: 16px;
}

/* 设备卡片样式 */
.equipment-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-panel);
}

.equipment-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-panel);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.equipment-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.equipment-card.warning {
  border-left: 4px solid var(--status-major);
}

.equipment-card.danger {
  border-left: 4px solid var(--status-critical);
}

.equipment-card.normal {
  border-left: 4px solid var(--status-normal);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.equipment-name {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-light {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.normal .status-light {
  background: var(--status-normal);
}

.status-indicator.warning .status-light {
  background: var(--status-major);
}

.status-indicator.danger .status-light {
  background: var(--status-critical);
}

.status-text {
  font-size: 11px;
  font-weight: var(--font-weight-medium);
}

.status-indicator.normal .status-text {
  color: var(--status-normal);
}

.status-indicator.warning .status-text {
  color: var(--status-major);
}

.status-indicator.danger .status-text {
  color: var(--status-critical);
}

.card-content {
  margin-bottom: var(--spacing-panel);
}

.data-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.data-label {
  color: var(--text-secondary);
  min-width: 80px;
}

.data-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
  margin-right: 8px;
}

.data-value.normal { color: var(--status-normal); }
.data-value.warning { color: var(--status-major); }
.data-value.danger { color: var(--status-critical); }

.data-unit {
  font-size: 10px;
  color: var(--text-secondary);
}

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-left: 8px;
}

.progress-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.progress-fill.normal { background: var(--status-normal); }
.progress-fill.warning { background: var(--status-major); }
.progress-fill.danger { background: var(--status-critical); }

.alert-badge {
  background: var(--status-critical);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  margin-left: 5px;
  animation: pulse 1s infinite;
}

.card-footer {
  text-align: right;
}

.detail-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: var(--secondary-color);
  transform: translateX(2px);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}

/* 安全风险等级监控样式 */
.risk-cards {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-base);
  margin-bottom: var(--spacing-panel);
}

.risk-card {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  padding: var(--spacing-base);
  cursor: pointer;
  transition: all 0.3s ease;
  border: 1px solid transparent;
  position: relative;
  overflow: hidden;
}

.risk-card::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 3px;
  transition: all 0.3s ease;
}

.risk-card:hover {
  transform: translateY(-2px);
  box-shadow: 0 4px 15px rgba(0, 0, 0, 0.3);
}

.risk-card.critical {
  border-color: rgba(244, 67, 54, 0.3);
}

.risk-card.critical::before {
  background: linear-gradient(90deg, #F44336, #E53935);
}

.risk-card.critical:hover {
  background: rgba(244, 67, 54, 0.1);
  box-shadow: 0 4px 15px rgba(244, 67, 54, 0.3);
}

.risk-card.major {
  border-color: rgba(255, 152, 0, 0.3);
}

.risk-card.major::before {
  background: linear-gradient(90deg, #FF9800, #FB8C00);
}

.risk-card.major:hover {
  background: rgba(255, 152, 0, 0.1);
  box-shadow: 0 4px 15px rgba(255, 152, 0, 0.3);
}

.risk-card.moderate {
  border-color: rgba(255, 193, 7, 0.3);
}

.risk-card.moderate::before {
  background: linear-gradient(90deg, #FFC107, #FFB300);
}

.risk-card.moderate:hover {
  background: rgba(255, 193, 7, 0.1);
  box-shadow: 0 4px 15px rgba(255, 193, 7, 0.3);
}

.risk-card.minor {
  border-color: rgba(33, 150, 243, 0.3);
}

.risk-card.minor::before {
  background: linear-gradient(90deg, #2196F3, #1E88E5);
}

.risk-card.minor:hover {
  background: rgba(33, 150, 243, 0.1);
  box-shadow: 0 4px 15px rgba(33, 150, 243, 0.3);
}

.risk-header {
  display: flex;
  align-items: center;
  gap: 6px;
  margin-bottom: 8px;
}

.risk-icon {
  font-size: 16px;
}

.risk-level {
  font-size: 12px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.risk-count {
  font-size: 24px;
  font-weight: var(--font-weight-bold);
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
  text-align: center;
  margin: 8px 0;
}

.risk-action {
  font-size: 10px;
  color: var(--text-secondary);
  text-align: center;
  opacity: 0.8;
}

.risk-card:hover .risk-action {
  opacity: 1;
  color: var(--text-primary);
}

/* 风险趋势图 */
.risk-trend {
  margin-top: var(--spacing-base);
}

.trend-title {
  font-size: 12px;
  color: var(--text-secondary);
  margin-bottom: 8px;
}

.trend-chart {
  height: 60px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 5px;
}

.trend-svg {
  width: 100%;
  height: 100%;
}

/* 设备详情弹窗样式 */
.detail-modal {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  bottom: 0;
  background: rgba(0, 0, 0, 0.8);
  display: flex;
  align-items: center;
  justify-content: center;
  z-index: 2000;
  backdrop-filter: blur(5px);
}

.detail-container {
  width: 90%;
  max-width: 1200px;
  height: 80%;
  background: var(--bg-panel);
  backdrop-filter: blur(20px);
  border: 1px solid var(--border-color);
  border-radius: 12px;
  display: flex;
  flex-direction: column;
  overflow: hidden;
}

.detail-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  padding: var(--spacing-panel);
  border-bottom: 1px solid var(--border-color);
  background: rgba(64, 196, 255, 0.1);
}

.detail-header h3 {
  color: var(--text-primary);
  font-size: var(--font-size-h2);
  margin: 0;
}

.close-btn {
  width: 32px;
  height: 32px;
  border: none;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-primary);
  border-radius: 50%;
  cursor: pointer;
  transition: all 0.3s ease;
}

.close-btn:hover {
  background: var(--status-critical);
  color: white;
}

.detail-content {
  flex: 1;
  display: flex;
  overflow: hidden;
}

.detail-left, .detail-right {
  flex: 1;
  padding: var(--spacing-panel);
  overflow-y: auto;
}

.detail-left {
  border-right: 1px solid var(--border-color);
}

.section-title {
  font-size: var(--font-size-h3);
  color: var(--primary-color);
  margin-bottom: var(--spacing-panel);
  font-weight: var(--font-weight-medium);
}

.detail-section {
  margin-bottom: var(--spacing-panel);
  padding: var(--spacing-base);
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
}

.detail-section h4 {
  color: var(--text-primary);
  font-size: 14px;
  margin: 0 0 var(--spacing-base) 0;
  font-weight: var(--font-weight-medium);
}

/* 塔体倾斜显示 */
.tilt-display {
  display: flex;
  align-items: center;
  gap: var(--spacing-panel);
}

.tilt-value {
  font-size: 24px;
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
}

.tilt-direction {
  font-size: 12px;
  color: var(--text-secondary);
}

.tilt-diagram {
  width: 80px;
  height: 80px;
}

.tilt-svg {
  width: 100%;
  height: 100%;
}

/* 位移列表 */
.displacement-list {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.displacement-item {
  display: flex;
  justify-content: space-between;
  padding: 5px 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 12px;
}

.displacement-item span:first-child {
  color: var(--text-secondary);
}

.displacement-item span:last-child {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
}

/* 环境数据 */
.environment-data {
  display: grid;
  grid-template-columns: 1fr 1fr 1fr;
  gap: 8px;
}

.env-item {
  display: flex;
  flex-direction: column;
  align-items: center;
  padding: 8px;
  background: rgba(255, 255, 255, 0.05);
  border-radius: 4px;
  font-size: 12px;
}

.env-item span:first-child {
  color: var(--text-secondary);
  margin-bottom: 3px;
}

.env-item span:last-child {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 视频区域 */
.video-feeds {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: var(--spacing-base);
}

.video-placeholder {
  aspect-ratio: 16/9;
  background: rgba(0, 0, 0, 0.5);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  border: 1px solid var(--border-color);
}

.video-icon {
  font-size: 24px;
  margin-bottom: 5px;
}

.video-label {
  font-size: 11px;
  color: var(--text-secondary);
}

/* 趋势图表 */
.trend-chart, .history-chart {
  width: 100%;
  height: 120px;
  background: rgba(0, 0, 0, 0.3);
  border-radius: var(--border-radius);
  border: 1px solid var(--border-color);
}

.chart-svg {
  width: 100%;
  height: 100%;
}

/* 告警历史 */
.alert-history {
  max-height: 120px;
  overflow-y: auto;
}

.alert-record {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  padding: 6px;
  margin-bottom: 5px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  font-size: 11px;
}

.alert-time {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  min-width: 40px;
}

.alert-msg {
  flex: 1;
  color: var(--text-primary);
}

.alert-status {
  padding: 2px 6px;
  border-radius: 10px;
  font-size: 9px;
}

.alert-status.resolved {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

/* AI分析 */
.ai-analysis {
  display: flex;
  gap: var(--spacing-base);
  padding: var(--spacing-base);
  background: rgba(64, 196, 255, 0.1);
  border-radius: var(--border-radius);
  border-left: 3px solid var(--primary-color);
}

.ai-icon {
  font-size: 20px;
}

.ai-text {
  flex: 1;
  font-size: 12px;
  color: var(--text-primary);
  line-height: 1.4;
}

/* 测点矩阵 */
.point-matrix {
  display: grid;
  grid-template-columns: repeat(3, 1fr);
  gap: 8px;
  margin-bottom: var(--spacing-base);
}

.matrix-point {
  aspect-ratio: 1;
  background: rgba(255, 255, 255, 0.05);
  border: 2px solid var(--border-color);
  border-radius: var(--border-radius);
  display: flex;
  flex-direction: column;
  align-items: center;
  justify-content: center;
  cursor: pointer;
  transition: all 0.3s ease;
}

.matrix-point:hover {
  transform: scale(1.05);
}

.matrix-point.normal {
  border-color: var(--status-normal);
  background: rgba(76, 175, 80, 0.1);
}

.matrix-point.warning {
  border-color: var(--status-major);
  background: rgba(255, 152, 0, 0.1);
}

.matrix-point.danger {
  border-color: var(--status-critical);
  background: rgba(244, 67, 54, 0.1);
  animation: pulse 2s infinite;
}

.point-number {
  font-size: 14px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 3px;
}

.point-value {
  font-size: 10px;
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

/* 实时数据 */
.realtime-data {
  max-height: 150px;
  overflow-y: auto;
}

.realtime-data .data-row {
  display: grid;
  grid-template-columns: 30px 40px 60px 40px;
  gap: 8px;
  padding: 6px;
  margin-bottom: 3px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  font-size: 11px;
  align-items: center;
}

.realtime-data .data-row.warning {
  background: rgba(255, 152, 0, 0.1);
}

.realtime-data .data-row.danger {
  background: rgba(244, 67, 54, 0.1);
}

.point-id {
  color: var(--text-secondary);
  text-align: center;
}

.point-type {
  color: var(--text-primary);
  text-align: center;
}

.point-value {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  text-align: center;
  font-family: 'Courier New', monospace;
}

.point-status {
  text-align: center;
  font-size: 10px;
}

.point-status.normal { color: var(--status-normal); }
.point-status.warning { color: var(--status-major); }
.point-status.danger { color: var(--status-critical); }

/* 沉降云图 */
.settlement-heatmap {
  text-align: center;
}

.heatmap-grid {
  display: grid;
  grid-template-columns: repeat(4, 1fr);
  gap: 2px;
  margin-bottom: 8px;
}

.heatmap-cell {
  aspect-ratio: 1;
  border-radius: 2px;
}

.heatmap-legend {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 8px;
  font-size: 10px;
  color: var(--text-secondary);
}

.legend-bar {
  width: 60px;
  height: 8px;
  background: linear-gradient(to right, #4CAF50, #FFEB3B, #FF5722);
  border-radius: 4px;
}

/* 关联事件 */
.related-events {
  max-height: 120px;
  overflow-y: auto;
}

.event-item {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
  padding: 6px;
  margin-bottom: 5px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  font-size: 11px;
}

.event-time {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
  min-width: 40px;
}

.event-desc {
  flex: 1;
  color: var(--text-primary);
}

.event-impact {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 人员与门禁样式 */
.personnel-stats {
  margin-bottom: var(--spacing-panel);
}

.total-personnel {
  text-align: center;
  margin-bottom: var(--spacing-base);
}

.personnel-number {
  font-size: 32px;
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
}

.personnel-label {
  font-size: 12px;
  color: var(--text-secondary);
  margin-top: 3px;
}

.personnel-breakdown {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 8px;
}

.personnel-type {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
}

.type-icon {
  font-size: 16px;
}

.type-info {
  flex: 1;
}

.type-name {
  font-size: 11px;
  color: var(--text-secondary);
}

.type-count {
  font-size: 13px;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

/* 门禁记录样式 */
.access-records {
  margin-bottom: var(--spacing-panel);
}

.records-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.header-title {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.header-time {
  font-size: 9px;
  color: var(--text-secondary);
}

.records-list {
  max-height: 100px;
  overflow-y: auto;
}

.access-record {
  display: grid;
  grid-template-columns: 35px 1fr 35px 50px;
  gap: 5px;
  padding: 5px;
  margin-bottom: 3px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: var(--border-radius);
  font-size: 9px;
  align-items: center;
}

.record-time {
  color: var(--text-secondary);
  font-family: 'Courier New', monospace;
}

.record-person {
  color: var(--text-primary);
}

.record-action {
  color: var(--primary-color);
  text-align: center;
}

.record-location {
  color: var(--text-secondary);
  font-size: 8px;
}

/* 异常告警样式 */
.access-alerts {
  margin-bottom: var(--spacing-panel);
}

.alerts-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 8px;
}

.alerts-title {
  font-size: 12px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.alerts-count {
  font-size: 11px;
  color: var(--text-secondary);
  padding: 2px 6px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 8px;
}

.alerts-count.danger {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.alerts-list {
  max-height: 80px;
  overflow-y: auto;
}

.access-alert {
  display: flex;
  align-items: flex-start;
  gap: 8px;
  padding: 8px;
  margin-bottom: 5px;
  border-radius: var(--border-radius);
  border-left: 3px solid;
}

.access-alert.danger {
  background: rgba(244, 67, 54, 0.1);
  border-left-color: #F44336;
}

.alert-icon {
  font-size: 14px;
  margin-top: 1px;
}

.alert-content {
  flex: 1;
}

.alert-message {
  font-size: 11px;
  color: var(--text-primary);
  margin-bottom: 2px;
}

.alert-time {
  font-size: 9px;
  color: var(--text-secondary);
}

.no-alerts {
  display: flex;
  align-items: center;
  justify-content: center;
  gap: 5px;
  padding: 15px;
  color: var(--text-secondary);
  font-size: 11px;
}

.no-alerts-icon {
  font-size: 14px;
}
</style>
