<template>
  <header class="dashboard-header glass-effect">
    <div class="header-content">
      <!-- 左侧：项目信息 -->
      <div class="header-left">
        <div class="project-info">
          <div class="project-icon">🏗️</div>
          <div class="project-details">
            <div class="project-name">某某电厂建设项目</div>
            <div class="project-phase">主体结构施工阶段</div>
          </div>
        </div>
      </div>

      <!-- 中心：系统标题和运行状态 -->
      <div class="header-center">
        <h1 class="main-title">电厂施工安全监控系统</h1>
        <div class="system-status">
          <div class="runtime-info">
            <span class="runtime-label">安全运行时长:</span>
            <span class="runtime-value">{{ runtimeDays }}天{{ runtimeHours }}小时{{ runtimeMinutes }}分</span>
          </div>
          <div class="safety-rating" :class="safetyLevel.class">
            <span class="rating-icon">{{ safetyLevel.icon }}</span>
            <span class="rating-text">{{ safetyLevel.text }}</span>
          </div>
        </div>
      </div>

      <!-- 右侧：时间和AI助手 -->
      <div class="header-right">
        <div class="time-display">
          <div class="current-time">{{ currentTime }}</div>
          <div class="current-date">{{ currentDate }}</div>
        </div>
        <div class="ai-assistant" @click="openAIAssistant">
          <div class="ai-icon">🤖</div>
          <div class="ai-text">AI助手</div>
          <div class="ai-status" :class="{ active: aiActive }"></div>
        </div>
      </div>
    </div>

    <!-- AI助手组件 -->
    <AIAssistant
      :visible="showAIAssistant"
      @close="closeAIAssistant"
      @action="handleAIAction" />
  </header>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import AIAssistant from './AIAssistant.vue'

const currentTime = ref('')
const currentDate = ref('')
const runtimeDays = ref(127)
const runtimeHours = ref(14)
const runtimeMinutes = ref(32)
const aiActive = ref(true)
const showAIAssistant = ref(false)

// 安全评级状态
const safetyRating = ref('green') // green, yellow, orange, red

const safetyLevel = computed(() => {
  const levels = {
    green: { class: 'safe', icon: '🟢', text: '安全' },
    yellow: { class: 'warning', icon: '🟡', text: '注意' },
    orange: { class: 'caution', icon: '🟠', text: '警告' },
    red: { class: 'danger', icon: '🔴', text: '危险' }
  }
  return levels[safetyRating.value] || levels.green
})

let timeInterval = null
let runtimeInterval = null

const updateTime = () => {
  const now = new Date()
  currentTime.value = now.toLocaleTimeString('zh-CN', {
    hour12: false,
    hour: '2-digit',
    minute: '2-digit',
    second: '2-digit'
  })
  currentDate.value = now.toLocaleDateString('zh-CN', {
    year: 'numeric',
    month: '2-digit',
    day: '2-digit',
    weekday: 'long'
  })
}

const updateRuntime = () => {
  runtimeMinutes.value++
  if (runtimeMinutes.value >= 60) {
    runtimeMinutes.value = 0
    runtimeHours.value++
    if (runtimeHours.value >= 24) {
      runtimeHours.value = 0
      runtimeDays.value++
    }
  }
}

const openAIAssistant = () => {
  showAIAssistant.value = true
}

const closeAIAssistant = () => {
  showAIAssistant.value = false
}

const handleAIAction = (action) => {
  console.log('AI助手执行操作:', action)
  // 这里可以根据不同的action类型执行相应的操作
  // 比如定位到3D模型、发送告警等
}

// 模拟安全评级变化
const updateSafetyRating = () => {
  const ratings = ['green', 'green', 'green', 'yellow', 'green', 'orange', 'green']
  const randomIndex = Math.floor(Math.random() * ratings.length)
  safetyRating.value = ratings[randomIndex]
}

onMounted(() => {
  updateTime()
  timeInterval = setInterval(updateTime, 1000)
  runtimeInterval = setInterval(updateRuntime, 60000) // 每分钟更新一次运行时长

  // 每30秒随机更新一次安全评级（仅用于演示）
  setInterval(updateSafetyRating, 30000)
})

onUnmounted(() => {
  if (timeInterval) {
    clearInterval(timeInterval)
  }
  if (runtimeInterval) {
    clearInterval(runtimeInterval)
  }
})
</script>

<style scoped>
.header-content {
  display: flex;
  align-items: center;
  justify-content: space-between;
  width: 100%;
  height: 100%;
  padding: 0 var(--spacing-panel);
}

.header-left {
  flex: 1;
  display: flex;
  align-items: center;
}

.project-info {
  display: flex;
  align-items: center;
  gap: var(--spacing-base);
}

.project-icon {
  font-size: 28px;
  color: var(--primary-color);
}

.project-details {
  display: flex;
  flex-direction: column;
}

.project-name {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  margin-bottom: 2px;
}

.project-phase {
  font-size: 12px;
  color: var(--text-secondary);
}

.header-center {
  flex: 2;
  text-align: center;
}

.main-title {
  font-size: var(--font-size-h1);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  margin-bottom: 8px;
  text-shadow: 0 0 10px rgba(64, 196, 255, 0.3);
}

.system-status {
  display: flex;
  justify-content: center;
  align-items: center;
  gap: 20px;
}

.runtime-info {
  font-size: 13px;
  color: var(--text-secondary);
}

.runtime-label {
  margin-right: 5px;
}

.runtime-value {
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
}

.safety-rating {
  display: flex;
  align-items: center;
  gap: 5px;
  padding: 4px 12px;
  border-radius: 12px;
  font-size: 13px;
  font-weight: var(--font-weight-medium);
}

.safety-rating.safe {
  background: rgba(76, 175, 80, 0.2);
  color: #4CAF50;
}

.safety-rating.warning {
  background: rgba(255, 193, 7, 0.2);
  color: #FFC107;
}

.safety-rating.caution {
  background: rgba(255, 152, 0, 0.2);
  color: #FF9800;
}

.safety-rating.danger {
  background: rgba(244, 67, 54, 0.2);
  color: #F44336;
}

.header-right {
  flex: 1;
  display: flex;
  justify-content: flex-end;
  align-items: center;
  gap: 20px;
}

.time-display {
  text-align: right;
}

.current-time {
  font-size: var(--font-size-h2);
  font-weight: var(--font-weight-medium);
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
}

.current-date {
  font-size: var(--font-size-base);
  color: var(--text-secondary);
  margin-top: 2px;
}

.ai-assistant {
  display: flex;
  align-items: center;
  gap: 8px;
  padding: 8px 12px;
  background: rgba(64, 196, 255, 0.1);
  border: 1px solid var(--primary-color);
  border-radius: 20px;
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.ai-assistant:hover {
  background: rgba(64, 196, 255, 0.2);
  transform: translateY(-1px);
}

.ai-icon {
  font-size: 16px;
}

.ai-text {
  font-size: 12px;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.ai-status {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  background: #666;
  position: absolute;
  top: 6px;
  right: 6px;
}

.ai-status.active {
  background: #4CAF50;
  animation: pulse 2s infinite;
}

@keyframes pulse {
  0% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0.7);
  }
  70% {
    box-shadow: 0 0 0 10px rgba(76, 175, 80, 0);
  }
  100% {
    box-shadow: 0 0 0 0 rgba(76, 175, 80, 0);
  }
}

/* 响应式调整 */
@media (max-width: 1366px) {
  .main-title {
    font-size: 24px;
  }

  .current-time {
    font-size: 16px;
  }

  .project-name {
    font-size: 14px;
  }

  .system-status {
    gap: 15px;
  }
}
</style>
