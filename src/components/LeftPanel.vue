<template>
  <div class="left-panel">
    <!-- 模块1: 实时天气监测 -->
    <div class="data-module glass-effect weather-module">
      <div class="module-title compact">
        <span class="module-icon">🌤️</span>
        实时天气监测
      </div>
      <div class="module-content compact">
        <!-- 1. 地面风速与高空风速对比 -->
        <div class="wind-comparison compact">
          <div class="weather-gauges compact">
            <div class="gauge-container surface-wind">
              <div class="gauge-header">
                <div class="gauge-icon">🌬️</div>
                <div class="gauge-title">地面风速</div>
              </div>
              <div class="gauge-wrapper compact">
                <svg class="wind-gauge compact" viewBox="0 0 120 70">
                  <defs>
                    <linearGradient id="surfaceWindGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" style="stop-color:#4CAF50"/>
                      <stop offset="30%" style="stop-color:#8BC34A"/>
                      <stop offset="60%" style="stop-color:#FFC107"/>
                      <stop offset="80%" style="stop-color:#FF9800"/>
                      <stop offset="100%" style="stop-color:#F44336"/>
                    </linearGradient>
                    <filter id="glow">
                      <feGaussianBlur stdDeviation="3" result="coloredBlur"/>
                      <feMerge> 
                        <feMergeNode in="coloredBlur"/>
                        <feMergeNode in="SourceGraphic"/>
                      </feMerge>
                    </filter>
                  </defs>
                  <circle cx="60" cy="60" r="35" stroke="rgba(64, 196, 255, 0.2)" stroke-width="1" fill="none"/>
                  <path d="M 25 60 A 35 35 0 0 1 95 60" stroke="rgba(255, 255, 255, 0.1)" stroke-width="4" fill="none"/>
                  <path d="M 25 60 A 35 35 0 0 1 95 60" stroke="url(#surfaceWindGradient)" stroke-width="3" fill="none"/>
                  <g stroke="rgba(255, 255, 255, 0.3)" stroke-width="1">
                    <line x1="25" y1="60" x2="28" y2="60"/>
                    <line x1="42" y1="35" x2="44" y2="37"/>
                    <line x1="60" y1="25" x2="60" y2="28"/>
                    <line x1="78" y1="35" x2="76" y2="37"/>
                    <line x1="95" y1="60" x2="92" y2="60"/>
                  </g>
                  <line :x1="60" :y1="60" 
                        :x2="60 + 25 * Math.cos((surfaceWindSpeed / 20 * 180 - 90) * Math.PI / 180)"
                        :y2="60 + 25 * Math.sin((surfaceWindSpeed / 20 * 180 - 90) * Math.PI / 180)"
                        stroke="var(--primary-color)" stroke-width="2" stroke-linecap="round" filter="url(#glow)"/>
                  <circle cx="60" cy="60" r="3" fill="var(--primary-color)" filter="url(#glow)"/>
                  <circle cx="60" cy="60" r="1.5" fill="white"/>
                </svg>
                <div class="gauge-display">
                  <div class="gauge-value">{{ surfaceWindSpeed.toFixed(2) }}</div>
                  <div class="gauge-unit">m/s</div>
                  <div class="gauge-direction">{{ getWindDirection(surfaceWindDirection) }}</div>
                  <div class="gauge-level">{{ getWindLevel(surfaceWindSpeed) }}</div>
                </div>
              </div>
            </div>

            <div class="gauge-container altitude-wind">
              <div class="gauge-header">
                <div class="gauge-icon">🌪️</div>
                <div class="gauge-title">高空风速</div>
              </div>
              <div class="gauge-wrapper compact">
                <svg class="wind-gauge compact" viewBox="0 0 120 70">
                  <defs>
                    <linearGradient id="altitudeWindGradient" x1="0%" y1="0%" x2="100%" y2="0%">
                      <stop offset="0%" style="stop-color:#4CAF50"/>
                      <stop offset="30%" style="stop-color:#8BC34A"/>
                      <stop offset="60%" style="stop-color:#FFC107"/>
                      <stop offset="80%" style="stop-color:#FF9800"/>
                      <stop offset="100%" style="stop-color:#F44336"/>
                    </linearGradient>
                  </defs>
                  <circle cx="60" cy="60" r="35" stroke="rgba(255, 152, 0, 0.2)" stroke-width="1" fill="none"/>
                  <path d="M 25 60 A 35 35 0 0 1 95 60" stroke="rgba(255, 255, 255, 0.1)" stroke-width="4" fill="none"/>
                  <path d="M 25 60 A 35 35 0 0 1 95 60" stroke="url(#altitudeWindGradient)" stroke-width="3" fill="none"/>
                  <g stroke="rgba(255, 255, 255, 0.3)" stroke-width="1">
                    <line x1="25" y1="60" x2="28" y2="60"/>
                    <line x1="42" y1="35" x2="44" y2="37"/>
                    <line x1="60" y1="25" x2="60" y2="28"/>
                    <line x1="78" y1="35" x2="76" y2="37"/>
                    <line x1="95" y1="60" x2="92" y2="60"/>
                  </g>
                  <line :x1="60" :y1="60" 
                        :x2="60 + 25 * Math.cos((highAltitudeWindSpeed / 30 * 180 - 90) * Math.PI / 180)"
                        :y2="60 + 25 * Math.sin((highAltitudeWindSpeed / 30 * 180 - 90) * Math.PI / 180)"
                        stroke="#FF9800" stroke-width="2" stroke-linecap="round" filter="url(#glow)"/>
                  <circle cx="60" cy="60" r="3" fill="#FF9800" filter="url(#glow)"/>
                  <circle cx="60" cy="60" r="1.5" fill="white"/>
                </svg>
                <div class="gauge-display">
                  <div class="gauge-value">{{ highAltitudeWindSpeed.toFixed(2) }}</div>
                  <div class="gauge-unit">m/s</div>
                  <div class="gauge-direction">{{ getWindDirection(highAltitudeWindDirection) }}</div>
                  <div class="gauge-level">{{ getWindLevel(highAltitudeWindSpeed) }}</div>
                </div>
              </div>
            </div>
          </div>
        </div>

        <!-- 2. 当前实时温度、湿度、能见度、气压 -->
        <div class="current-weather compact">
          <div class="weather-params compact">
            <div class="param-row">
              <div class="param-item">
                <span class="param-icon">🌡️</span>
                <span class="param-label">温度</span>
                <span class="param-value">{{ temperature.toFixed(2) }}°C</span>
              </div>
              <div class="param-item">
                <span class="param-icon">💧</span>
                <span class="param-label">湿度</span>
                <span class="param-value">{{ humidity }}%</span>
              </div>
            </div>
            <div class="param-row">
              <div class="param-item">
                <span class="param-icon">☁️</span>
                <span class="param-label">能见度</span>
                <span class="param-value">{{ visibility.toFixed(2) }} km</span>
              </div>
              <div class="param-item">
                <span class="param-icon">🌪️</span>
                <span class="param-label">气压</span>
                <span class="param-value">{{ pressure.toFixed(2) }} hPa</span>
              </div>
            </div>
          </div>
        </div>

        <!-- 3-6. 预警卡片 -->
        <div class="warnings-compact">
          <!-- 大风预警 -->
          <div class="warning-card-mini wind-warning" :class="windWarning.level">
            <div class="warning-header-mini">
              <span class="warning-icon-mini">💨</span>
              <span class="warning-title-mini">大风预警</span>
              <span class="warning-level-mini">{{ windWarning.levelText }}</span>
            </div>
            <div class="warning-content-mini">
              <span class="warning-text">{{ windWarning.operationText }}</span>
            </div>
          </div>

          <!-- 沙尘暴预警 -->
          <div class="warning-card-mini dust-warning" :class="dustWarning.level">
            <div class="warning-header-mini">
              <span class="warning-icon-mini">🌪️</span>
              <span class="warning-title-mini">沙尘暴预警</span>
              <span class="warning-level-mini">{{ dustWarning.levelText }}</span>
            </div>
            <div class="warning-content-mini">
              <span class="warning-text">{{ dustWarning.operationText }}</span>
            </div>
          </div>

          <!-- 极端温度预警 -->
          <div class="warning-card-mini temp-warning" :class="tempWarning.level">
            <div class="warning-header-mini">
              <span class="warning-icon-mini">🌡️</span>
              <span class="warning-title-mini">极端温度</span>
              <span class="warning-level-mini">{{ tempWarning.levelText }}</span>
            </div>
            <div class="warning-content-mini">
              <span class="warning-text">{{ tempWarning.operationText }}</span>
            </div>
          </div>

          <!-- 降水雷电预警 -->
          <div class="warning-card-mini rain-warning" :class="rainWarning.level">
            <div class="warning-header-mini">
              <span class="warning-icon-mini">⛈️</span>
              <span class="warning-title-mini">降水雷电</span>
              <span class="warning-level-mini">{{ rainWarning.levelText }}</span>
            </div>
            <div class="warning-content-mini">
              <span class="warning-text">{{ rainWarning.operationText }}</span>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 模块2: 气象预报与地震监测 -->
    <div class="data-module glass-effect">
      <div class="module-title compact">
        <span class="module-icon">📊</span>
        气象预报与地震监测
      </div>
      <div class="module-content compact">
        <!-- 气象预报部分 -->
        <div class="forecast-section compact">
          <div class="section-header compact">
            <span class="section-title">气象预报</span>
            <div class="forecast-tabs">
              <button class="tab-btn" :class="{ active: forecastPeriod === '24h' }" @click="forecastPeriod = '24h'">24h</button>
              <button class="tab-btn" :class="{ active: forecastPeriod === '72h' }" @click="forecastPeriod = '72h'">72h</button>
            </div>
          </div>
          <div class="forecast-list compact">
            <div v-for="forecast in currentForecast" :key="forecast.time" class="forecast-item compact">
              <div class="forecast-time">{{ forecast.time }}</div>
              <div class="forecast-weather">
                <span class="weather-icon">{{ forecast.icon }}</span>
                <span class="weather-desc">{{ forecast.desc }}</span>
              </div>
              <div class="forecast-wind">{{ forecast.windSpeed }}m/s</div>
              <div class="forecast-temp">{{ forecast.temp }}°C</div>
            </div>
          </div>
        </div>

        <!-- 地震监测部分 -->
        <div class="seismic-section compact">
          <div class="section-header compact">
            <span class="section-title">地震监测</span>
            <div class="pga-display-mini">
              <div class="pga-value" :class="getPGAStatus(currentPGA)">{{ currentPGA.toFixed(3) }}g</div>
              <div class="pga-status">{{ getPGAStatusText(currentPGA) }}</div>
            </div>
          </div>
          <div class="pga-chart-compact">
            <div class="pga-bars">
              <div v-for="(value, index) in pgaHistory" :key="index"
                   class="pga-bar"
                   :style="{
                     height: (value / Math.max(...pgaHistory) * 100) + '%',
                     backgroundColor: getPGAColor(value)
                   }">
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <!-- 关键设备安全管理 -->
    <EquipmentManagement />
  </div>
</template>

<script setup>
import { ref, computed, onMounted, onUnmounted } from 'vue'
import EquipmentManagement from './EquipmentManagement.vue'

// 气象数据
const surfaceWindSpeed = ref(8.5)
const surfaceWindDirection = ref(135)
const highAltitudeWindSpeed = ref(15.2)
const highAltitudeWindDirection = ref(142)
const temperature = ref(28.5)
const humidity = ref(65)
const pressure = ref(1013.2)
const visibility = ref(12.5)

// 气象预报数据
const forecastPeriod = ref('24h')
const forecast24h = ref([
  { time: '14:00', icon: '☀️', desc: '晴', windSpeed: 12, windDirection: 'NW', temp: 32 },
  { time: '16:00', icon: '⛅', desc: '多云', windSpeed: 15, windDirection: 'NW', temp: 30 },
  { time: '18:00', icon: '🌤️', desc: '晴转多云', windSpeed: 18, windDirection: 'N', temp: 28 }
])

const forecast72h = ref([
  { time: '明日', icon: '🌪️', desc: '大风', windSpeed: 25, windDirection: 'NW', temp: 28 },
  { time: '后天', icon: '🌫️', desc: '沙尘', windSpeed: 18, windDirection: 'W', temp: 26 },
  { time: '第3天', icon: '☀️', desc: '晴', windSpeed: 12, windDirection: 'SW', temp: 30 }
])

const currentForecast = computed(() => {
  return forecastPeriod.value === '24h' ? forecast24h.value : forecast72h.value
})

// 地震监测数据
const currentPGA = ref(0.025)
const pgaHistory = ref([
  0.015, 0.018, 0.022, 0.025, 0.028, 0.032, 0.029, 0.025,
  0.021, 0.018, 0.015, 0.012, 0.014, 0.017, 0.020, 0.023
])

// 沙漠地区特色预警数据
const windWarning = ref({
  level: 'warning',
  levelText: '橙色',
  operationText: '停止高空作业'
})

const dustWarning = ref({
  level: 'info',
  levelText: '蓝色',
  operationText: '加强防护'
})

const tempWarning = ref({
  level: 'warning',
  levelText: '黄色',
  operationText: '注意防护'
})

const rainWarning = ref({
  level: 'normal',
  levelText: '无预警',
  operationText: '正常作业'
})

// 方法
const getPGAStatus = (value) => {
  if (value >= 0.1) return 'danger'
  if (value >= 0.05) return 'warning'
  return 'normal'
}

const getPGAStatusText = (value) => {
  if (value >= 0.1) return '地震报警'
  if (value >= 0.05) return '地震预警'
  return '正常'
}

const getPGAColor = (value) => {
  if (value >= 0.1) return 'var(--status-critical)'
  if (value >= 0.05) return 'var(--status-major)'
  return 'var(--status-normal)'
}

const getWindDirection = (degree) => {
  const directions = ['N', 'NE', 'E', 'SE', 'S', 'SW', 'W', 'NW']
  const index = Math.round(degree / 45) % 8
  return directions[index]
}

const getWindLevel = (speed) => {
  if (speed < 3) return '微风'
  if (speed < 6) return '轻风'
  if (speed < 12) return '和风'
  if (speed < 20) return '清风'
  if (speed < 29) return '强风'
  return '烈风'
}

// 数据更新
let weatherInterval = null

const updateWeatherData = () => {
  surfaceWindSpeed.value = Math.random() * 5 + 6
  surfaceWindDirection.value = Math.floor(Math.random() * 360)
  highAltitudeWindSpeed.value = Math.random() * 10 + 10
  highAltitudeWindDirection.value = Math.floor(Math.random() * 360)
  temperature.value = Math.random() * 10 + 20
  humidity.value = Math.floor(Math.random() * 30) + 50
  pressure.value = Math.random() * 20 + 1000
  visibility.value = Math.random() * 10 + 5
  currentPGA.value = Math.random() * 0.08 + 0.01
  pgaHistory.value.shift()
  pgaHistory.value.push(currentPGA.value)
}

onMounted(() => {
  weatherInterval = setInterval(updateWeatherData, 5000)
})

onUnmounted(() => {
  if (weatherInterval) {
    clearInterval(weatherInterval)
  }
})
</script>

<style scoped>
/* 左侧面板基础样式 - 16:9自适应 */
.left-panel {
  width: 100%;
  height: 100%;
  overflow: hidden;
  padding: 6px;
  background: rgba(16, 26, 48, 0.8);
  backdrop-filter: blur(10px);
  border-right: 1px solid var(--border-color);
  display: flex;
  flex-direction: column;
  gap: 6px;
}

/* 模块样式 */
.data-module {
  background: rgba(255, 255, 255, 0.05);
  border-radius: 8px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  overflow: hidden;
}

.weather-module {
  flex: 0.4;
  min-height: 0;
  max-height: 40%;
}

.module-title.compact {
  padding: 8px 12px;
  font-size: 13px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
  background: rgba(255, 255, 255, 0.03);
  border-bottom: 1px solid rgba(255, 255, 255, 0.1);
  display: flex;
  align-items: center;
  gap: 6px;
}

.module-content.compact {
  padding: 6px;
  height: calc(100% - 32px);
  overflow: hidden;
  display: flex;
  flex-direction: column;
}

/* 为其他模块添加flex分配 */
.data-module:nth-child(2) {
  flex: 0.35;
  min-height: 0;
}

.data-module:nth-child(3) {
  flex: 0.25;
  min-height: 0;
}

/* 风速仪表盘紧凑样式 */
.wind-comparison.compact {
  margin-bottom: 8px;
}

.weather-gauges.compact {
  display: flex;
  gap: 8px;
  margin-bottom: 8px;
}

.gauge-container {
  flex: 1;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 6px;
  padding: 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.gauge-container.surface-wind {
  border-left: 2px solid var(--primary-color);
}

.gauge-container.altitude-wind {
  border-left: 2px solid #FF9800;
}

.gauge-header {
  display: flex;
  align-items: center;
  gap: 4px;
  margin-bottom: 4px;
}

.gauge-icon {
  font-size: 12px;
}

.gauge-title {
  font-size: 10px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
}

.gauge-wrapper.compact {
  display: flex;
  align-items: center;
  gap: 6px;
}

.wind-gauge.compact {
  flex: 1;
  height: 50px;
}

.gauge-display {
  flex-shrink: 0;
  text-align: center;
  min-width: 60px;
}

.gauge-value {
  font-size: 14px;
  font-weight: var(--font-weight-bold);
  color: var(--primary-color);
  font-family: 'Courier New', monospace;
  line-height: 1;
}

.gauge-unit {
  font-size: 8px;
  color: var(--text-secondary);
  margin-top: 1px;
}

.gauge-direction {
  font-size: 9px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  margin-top: 2px;
}

.gauge-level {
  font-size: 8px;
  color: var(--text-tertiary);
  margin-top: 1px;
}

/* 气象参数紧凑样式 */
.current-weather.compact {
  margin-bottom: 8px;
}

.weather-params.compact {
  display: flex;
  flex-direction: column;
  gap: 4px;
}

.param-row {
  display: flex;
  gap: 4px;
}

.param-item {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 4px;
  padding: 4px 6px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  border: 1px solid rgba(255, 255, 255, 0.1);
}

.param-icon {
  font-size: 10px;
}

.param-label {
  font-size: 9px;
  color: var(--text-secondary);
  min-width: 30px;
}

.param-value {
  font-size: 9px;
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
}

/* 紧凑预警卡片样式 */
.warnings-compact {
  display: grid;
  grid-template-columns: 1fr 1fr;
  gap: 4px;
}

.warning-card-mini {
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  padding: 4px 6px;
  border: 1px solid rgba(255, 255, 255, 0.1);
  position: relative;
  overflow: hidden;
}

.warning-card-mini::before {
  content: '';
  position: absolute;
  top: 0;
  left: 0;
  right: 0;
  height: 2px;
}

.warning-card-mini.warning::before {
  background: #FF9800;
}

.warning-card-mini.info::before {
  background: #2196F3;
}

.warning-card-mini.normal::before {
  background: #4CAF50;
}

.warning-header-mini {
  display: flex;
  align-items: center;
  gap: 3px;
  margin-bottom: 2px;
}

.warning-icon-mini {
  font-size: 10px;
}

.warning-title-mini {
  flex: 1;
  font-size: 8px;
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.warning-level-mini {
  font-size: 7px;
  padding: 1px 3px;
  border-radius: 6px;
  background: rgba(255, 255, 255, 0.1);
  color: var(--text-secondary);
}

.warning-content-mini {
  font-size: 7px;
}

.warning-text {
  color: var(--text-secondary);
  line-height: 1.2;
}

/* 其他模块紧凑样式 */
.forecast-section.compact,
.seismic-section.compact {
  margin-bottom: 8px;
}

.section-header.compact {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: 6px;
}

.section-title {
  font-size: 11px;
  color: var(--primary-color);
  font-weight: var(--font-weight-medium);
}

.forecast-tabs {
  display: flex;
  gap: 4px;
}

.tab-btn {
  padding: 2px 6px;
  font-size: 8px;
  background: rgba(255, 255, 255, 0.1);
  border: 1px solid rgba(255, 255, 255, 0.2);
  border-radius: 4px;
  color: var(--text-secondary);
  cursor: pointer;
  transition: all 0.3s ease;
}

.tab-btn.active {
  background: var(--primary-color);
  color: white;
}

.forecast-list.compact {
  display: flex;
  flex-direction: column;
  gap: 3px;
}

.forecast-item.compact {
  display: flex;
  align-items: center;
  gap: 6px;
  padding: 3px 6px;
  background: rgba(255, 255, 255, 0.03);
  border-radius: 4px;
  font-size: 8px;
}

.forecast-time {
  min-width: 30px;
  color: var(--text-secondary);
}

.forecast-weather {
  flex: 1;
  display: flex;
  align-items: center;
  gap: 3px;
}

.weather-icon {
  font-size: 10px;
}

.weather-desc {
  color: var(--text-primary);
}

.forecast-wind,
.forecast-temp {
  font-size: 8px;
  color: var(--text-primary);
  font-family: 'Courier New', monospace;
}

.pga-display-mini {
  display: flex;
  align-items: center;
  gap: 6px;
}

.pga-value {
  font-size: 10px;
  font-weight: var(--font-weight-bold);
  font-family: 'Courier New', monospace;
}

.pga-value.normal { color: var(--status-normal); }
.pga-value.warning { color: var(--status-major); }
.pga-value.danger { color: var(--status-critical); }

.pga-status {
  font-size: 8px;
  color: var(--text-secondary);
}

.pga-chart-compact {
  height: 30px;
  background: rgba(0, 0, 0, 0.2);
  border-radius: 4px;
  padding: 3px;
  margin-top: 4px;
}

.pga-bars {
  display: flex;
  align-items: end;
  height: 100%;
  gap: 1px;
}

.pga-bar {
  flex: 1;
  min-height: 2px;
  border-radius: 1px;
}
</style>
