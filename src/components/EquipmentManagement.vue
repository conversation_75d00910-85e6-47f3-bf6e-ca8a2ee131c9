<template>
  <div class="equipment-management">
    <!-- 关键设备安全管理 -->
    <div class="data-module glass-effect">
      <div class="module-title">
        <span class="module-icon">🏗️</span>
        关键设备安全管理
      </div>
      <div class="module-content">
        <!-- 设备卡片列表 -->
        <div class="equipment-cards">
          <!-- 1号塔吊卡片 -->
          <div class="equipment-card" :class="cranes[0].status" @click="showEquipmentDetails(cranes[0], 'crane')">
            <div class="card-header">
              <div class="equipment-name">{{ cranes[0].name }}</div>
              <div class="status-indicator" :class="cranes[0].status">
                <div class="status-light"></div>
                <span class="status-text">{{ getStatusText(cranes[0].status) }}</span>
              </div>
            </div>
            
            <div class="card-content">
              <div class="data-row">
                <span class="data-label">实时倾斜度:</span>
                <span class="data-value" :class="getTiltStatus(cranes[0].tilt)">
                  {{ cranes[0].tilt.toFixed(2) }}°
                </span>
                <div class="progress-bar">
                  <div class="progress-fill" 
                       :style="{ width: Math.min(cranes[0].tilt / 2 * 100, 100) + '%' }"
                       :class="getTiltStatus(cranes[0].tilt)"></div>
                </div>
              </div>
              
              <div class="data-row">
                <span class="data-label">实时风速:</span>
                <span class="data-value">{{ cranes[0].windSpeed }} km/h</span>
                <span class="data-unit">(高空)</span>
              </div>
              
              <div class="data-row">
                <span class="data-label">今日告警:</span>
                <span class="data-value" :class="cranes[0].alertCount > 0 ? 'warning' : 'normal'">
                  {{ cranes[0].alertCount }} 条
                </span>
              </div>
            </div>
            
            <div class="card-footer">
              <button class="detail-btn" @click.stop="showEquipmentDetails(cranes[0], 'crane')">
                查看详情 >
              </button>
            </div>
          </div>

          <!-- C区高支模卡片 -->
          <div class="equipment-card" :class="scaffolds[0].status" @click="showEquipmentDetails(scaffolds[0], 'scaffold')">
            <div class="card-header">
              <div class="equipment-name">{{ scaffolds[0].name }}</div>
              <div class="status-indicator" :class="scaffolds[0].status">
                <div class="status-light"></div>
                <span class="status-text">{{ getStatusText(scaffolds[0].status) }}</span>
              </div>
            </div>
            
            <div class="card-content">
              <div class="data-row">
                <span class="data-label">最大沉降:</span>
                <span class="data-value" :class="getSettlementStatus(scaffolds[0].maxSettlement)">
                  {{ scaffolds[0].maxSettlement.toFixed(1) }} mm
                </span>
              </div>
              
              <div class="data-row">
                <span class="data-label">最大轴力:</span>
                <span class="data-value" :class="getAxialStatus(scaffolds[0].maxAxial)">
                  {{ scaffolds[0].maxAxial.toFixed(1) }} kN
                </span>
              </div>
              
              <div class="data-row">
                <span class="data-label">异常测点:</span>
                <span class="data-value" :class="scaffolds[0].abnormalPoints > 0 ? 'danger' : 'normal'">
                  {{ scaffolds[0].abnormalPoints }} 个
                </span>
                <span v-if="scaffolds[0].abnormalPoints > 0" class="alert-badge">!</span>
              </div>
            </div>
            
            <div class="card-footer">
              <button class="detail-btn" @click.stop="showEquipmentDetails(scaffolds[0], 'scaffold')">
                查看详情 >
              </button>
            </div>
          </div>

          <!-- 2号塔吊卡片 -->
          <div class="equipment-card" :class="cranes[1].status" @click="showEquipmentDetails(cranes[1], 'crane')">
            <div class="card-header">
              <div class="equipment-name">{{ cranes[1].name }}</div>
              <div class="status-indicator" :class="cranes[1].status">
                <div class="status-light"></div>
                <span class="status-text">{{ getStatusText(cranes[1].status) }}</span>
              </div>
            </div>
            
            <div class="card-content">
              <div class="data-row">
                <span class="data-label">实时倾斜度:</span>
                <span class="data-value" :class="getTiltStatus(cranes[1].tilt)">
                  {{ cranes[1].tilt.toFixed(2) }}°
                </span>
                <div class="progress-bar">
                  <div class="progress-fill" 
                       :style="{ width: Math.min(cranes[1].tilt / 2 * 100, 100) + '%' }"
                       :class="getTiltStatus(cranes[1].tilt)"></div>
                </div>
              </div>
              
              <div class="data-row">
                <span class="data-label">实时风速:</span>
                <span class="data-value">{{ cranes[1].windSpeed }} km/h</span>
                <span class="data-unit">(高空)</span>
              </div>
              
              <div class="data-row">
                <span class="data-label">今日告警:</span>
                <span class="data-value" :class="cranes[1].alertCount > 0 ? 'warning' : 'normal'">
                  {{ cranes[1].alertCount }} 条
                </span>
              </div>
            </div>
            
            <div class="card-footer">
              <button class="detail-btn" @click.stop="showEquipmentDetails(cranes[1], 'crane')">
                查看详情 >
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>
</template>

<script setup>
import { ref, onMounted, onUnmounted } from 'vue'

// 塔吊数据
const cranes = ref([
  { 
    id: 1, 
    name: '1号塔吊', 
    status: 'normal',
    tilt: 0.21,
    windSpeed: 25,
    alertCount: 0
  },
  { 
    id: 2, 
    name: '2号塔吊', 
    status: 'warning',
    tilt: 1.2,
    windSpeed: 28,
    alertCount: 2
  }
])

// 高支模数据
const scaffolds = ref([
  {
    id: 1,
    name: 'C区高支模',
    status: 'warning',
    maxSettlement: -1.5,
    maxAxial: 35.2,
    abnormalPoints: 1
  }
])

// 方法
const getStatusText = (status) => {
  const statusMap = {
    normal: '正常',
    warning: '预警',
    danger: '报警'
  }
  return statusMap[status] || '未知'
}

const getTiltStatus = (tilt) => {
  if (tilt >= 1.5) return 'danger'
  if (tilt >= 1.0) return 'warning'
  return 'normal'
}

const getSettlementStatus = (settlement) => {
  const absValue = Math.abs(settlement)
  if (absValue >= 3.0) return 'danger'
  if (absValue >= 2.0) return 'warning'
  return 'normal'
}

const getAxialStatus = (axial) => {
  if (axial >= 40) return 'warning'
  if (axial >= 45) return 'danger'
  return 'normal'
}

const showEquipmentDetails = (equipment, type) => {
  console.log(`显示${equipment.name}详情，类型：${type}`)
  // 这里可以触发详情弹窗
}

// 数据更新
let updateInterval = null

const updateData = () => {
  cranes.value.forEach(crane => {
    const baseTilt = crane.id === 1 ? 0.21 : 1.2
    crane.tilt = baseTilt + (Math.random() - 0.5) * 0.3
    crane.windSpeed = Math.floor(Math.random() * 10) + 20
    
    if (crane.tilt >= 1.5) crane.status = 'danger'
    else if (crane.tilt >= 1.0) crane.status = 'warning'
    else crane.status = 'normal'
  })
  
  scaffolds.value.forEach(scaffold => {
    scaffold.maxSettlement = -(Math.random() * 2 + 1)
    scaffold.maxAxial = Math.random() * 10 + 30
    scaffold.abnormalPoints = Math.floor(Math.random() * 3)
    
    if (scaffold.abnormalPoints > 1) scaffold.status = 'danger'
    else if (scaffold.abnormalPoints > 0) scaffold.status = 'warning'
    else scaffold.status = 'normal'
  })
}

onMounted(() => {
  updateInterval = setInterval(updateData, 5000)
})

onUnmounted(() => {
  if (updateInterval) {
    clearInterval(updateInterval)
  }
})
</script>

<style scoped>
/* 设备卡片样式 */
.equipment-cards {
  display: flex;
  flex-direction: column;
  gap: var(--spacing-panel);
}

.equipment-card {
  background: rgba(255, 255, 255, 0.05);
  border: 1px solid var(--border-color);
  border-radius: var(--border-radius);
  padding: var(--spacing-panel);
  cursor: pointer;
  transition: all 0.3s ease;
  position: relative;
}

.equipment-card:hover {
  background: rgba(255, 255, 255, 0.08);
  transform: translateY(-2px);
  box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
}

.equipment-card.warning {
  border-left: 4px solid var(--status-major);
}

.equipment-card.danger {
  border-left: 4px solid var(--status-critical);
}

.equipment-card.normal {
  border-left: 4px solid var(--status-normal);
}

.card-header {
  display: flex;
  justify-content: space-between;
  align-items: center;
  margin-bottom: var(--spacing-base);
}

.equipment-name {
  font-size: var(--font-size-h3);
  font-weight: var(--font-weight-medium);
  color: var(--text-primary);
}

.status-indicator {
  display: flex;
  align-items: center;
  gap: 5px;
}

.status-light {
  width: 8px;
  height: 8px;
  border-radius: 50%;
  animation: pulse 2s infinite;
}

.status-indicator.normal .status-light {
  background: var(--status-normal);
}

.status-indicator.warning .status-light {
  background: var(--status-major);
}

.status-indicator.danger .status-light {
  background: var(--status-critical);
}

.status-text {
  font-size: 11px;
  font-weight: var(--font-weight-medium);
}

.card-content {
  margin-bottom: var(--spacing-panel);
}

.data-row {
  display: flex;
  align-items: center;
  margin-bottom: 8px;
  font-size: 12px;
}

.data-label {
  color: var(--text-secondary);
  min-width: 80px;
}

.data-value {
  color: var(--text-primary);
  font-weight: var(--font-weight-medium);
  font-family: 'Courier New', monospace;
  margin-right: 8px;
}

.data-value.normal { color: var(--status-normal); }
.data-value.warning { color: var(--status-major); }
.data-value.danger { color: var(--status-critical); }

.progress-bar {
  flex: 1;
  height: 4px;
  background: rgba(255, 255, 255, 0.1);
  border-radius: 2px;
  overflow: hidden;
  margin-left: 8px;
}

.progress-fill {
  height: 100%;
  transition: all 0.3s ease;
}

.progress-fill.normal { background: var(--status-normal); }
.progress-fill.warning { background: var(--status-major); }
.progress-fill.danger { background: var(--status-critical); }

.alert-badge {
  background: var(--status-critical);
  color: white;
  border-radius: 50%;
  width: 16px;
  height: 16px;
  display: flex;
  align-items: center;
  justify-content: center;
  font-size: 10px;
  font-weight: bold;
  margin-left: 5px;
  animation: pulse 1s infinite;
}

.card-footer {
  text-align: right;
}

.detail-btn {
  background: var(--primary-color);
  color: white;
  border: none;
  padding: 6px 12px;
  border-radius: var(--border-radius);
  font-size: 11px;
  cursor: pointer;
  transition: all 0.3s ease;
}

.detail-btn:hover {
  background: var(--secondary-color);
  transform: translateX(2px);
}

@keyframes pulse {
  0% {
    transform: scale(1);
    opacity: 1;
  }
  50% {
    transform: scale(1.1);
    opacity: 0.7;
  }
  100% {
    transform: scale(1);
    opacity: 1;
  }
}
</style>
