<script setup lang="ts">
import Header from '../components/Header.vue'
import LeftPanel from '../components/LeftPanel.vue'
import CenterMap from '../components/CenterMap.vue'
import RightPanel from '../components/RightPanel.vue'
import AlertCenter from '../components/AlertCenter.vue'
import NotificationCenter from '../components/NotificationCenter.vue'
</script>

<template>
  <div class="aspect-ratio-container">
    <div class="aspect-ratio-content">
      <div class="dashboard-layout">
        <!-- 顶部标题 -->
        <Header />

        <!-- 主要内容区域 -->
        <div class="dashboard-content">
          <!-- 左侧数据面板 -->
          <LeftPanel />

          <!-- 中心3D模型区域 -->
          <CenterMap />

          <!-- 右侧数据面板 -->
          <RightPanel />
        </div>

        <!-- 底部告警中心 -->
        <AlertCenter />

        <!-- 推送通知中心 -->
        <NotificationCenter />
      </div>
    </div>
  </div>
</template>

<style scoped>
/* 单屏固定布局 - 经典三栏式 16:9 */
.aspect-ratio-container {
  width: 100vw;
  height: 100vh;
  background: var(--bg-main);
  display: flex;
  align-items: center;
  justify-content: center;
  overflow: hidden;
}

.aspect-ratio-content {
  width: 100%;
  height: 100%;
  max-width: 177.78vh; /* 16:9比例 */
  max-height: 56.25vw; /* 9:16比例 */
  position: relative;
}

.dashboard-layout {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  overflow: hidden;
  background: var(--bg-main);
}

/* A. 标题区域 - 固定顶部 */
.dashboard-layout > :first-child {
  position: fixed;
  top: 0;
  left: 0;
  right: 0;
  height: 80px;
  width: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  z-index: 1000;
  flex-shrink: 0;
}

/* 主要内容区域 - 三栏布局 */
.dashboard-content {
  display: flex;
  height: calc(100% - 80px);
  margin-top: 80px;
  overflow: hidden;
}

/* B. 左侧数据面板 - 25%宽度 */
.dashboard-content > :first-child {
  width: 25%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--spacing-panel);
  overflow: hidden;
  flex-shrink: 0;
}

/* C. 中心区域 - 50%宽度 */
.dashboard-content > :nth-child(2) {
  width: 50%;
  height: 100%;
  padding: var(--spacing-panel);
  overflow: hidden;
  flex-shrink: 0;
}

/* D. 右侧数据面板 - 25%宽度 */
.dashboard-content > :last-child {
  width: 25%;
  height: 100%;
  background: var(--bg-glass);
  backdrop-filter: blur(10px);
  -webkit-backdrop-filter: blur(10px);
  padding: var(--spacing-panel);
  overflow: hidden;
  flex-shrink: 0;
}

/* 底部告警中心 - 绝对定位 */
.dashboard-layout > :nth-child(3) {
  position: absolute;
  bottom: 0;
  left: 0;
  right: 0;
  height: auto;
  z-index: 999;
}

/* 推送通知中心 - 绝对定位 */
.dashboard-layout > :last-child {
  position: absolute;
  top: 90px;
  right: 20px;
  z-index: 1001;
}

/* 响应式设计 - 基准1920x1080px */
@media (max-width: 1600px) {
  .dashboard-content > :first-child,
  .dashboard-content > :last-child {
    width: 22%;
  }

  .dashboard-content > :nth-child(2) {
    width: 56%;
  }
}

@media (max-width: 1366px) {
  .dashboard-content > :first-child,
  .dashboard-content > :last-child {
    width: 20%;
  }

  .dashboard-content > :nth-child(2) {
    width: 60%;
  }

  .dashboard-layout > :first-child {
    height: 70px;
  }
}

@media (max-width: 1024px) {
  .dashboard-content > :first-child,
  .dashboard-content > :last-child {
    width: 18%;
  }

  .dashboard-content > :nth-child(2) {
    width: 64%;
  }

  .dashboard-layout > :first-child {
    height: 60px;
  }
}

/* 确保所有子组件不产生滚动条 */
.dashboard-layout * {
  box-sizing: border-box;
}

.dashboard-layout *::-webkit-scrollbar {
  display: none;
}

.dashboard-layout * {
  -ms-overflow-style: none;
  scrollbar-width: none;
}
</style>
