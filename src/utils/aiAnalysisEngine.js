/**
 * AI分析引擎 - 工地助手的核心智能分析模块
 * 负责跨系统数据关联分析、风险预测、决策支持
 */

export class AIAnalysisEngine {
  constructor() {
    this.knowledgeBase = new KnowledgeBase()
    this.riskModels = new RiskModels()
    this.dataCorrelator = new DataCorrelator()
  }

  /**
   * 综合风险分析
   * @param {Object} systemData - 系统各模块数据
   * @returns {Object} 风险分析结果
   */
  analyzeComprehensiveRisk(systemData) {
    const {
      weather,
      cranes,
      scaffolds,
      personnel,
      seismic,
      alerts
    } = systemData

    // 1. 单项风险评估
    const weatherRisk = this.assessWeatherRisk(weather)
    const structuralRisk = this.assessStructuralRisk(cranes, scaffolds)
    const personnelRisk = this.assessPersonnelRisk(personnel)
    const seismicRisk = this.assessSeismicRisk(seismic)

    // 2. 跨系统关联分析
    const correlatedRisks = this.dataCorrelator.analyzeCorrelations({
      weather: weatherRisk,
      structural: structuralRisk,
      personnel: personnelRisk,
      seismic: seismicRisk
    })

    // 3. 综合风险等级计算
    const overallRisk = this.calculateOverallRisk(correlatedRisks)

    return {
      overall: overallRisk,
      categories: {
        weather: weatherRisk,
        structural: structuralRisk,
        personnel: personnelRisk,
        seismic: seismicRisk
      },
      correlations: correlatedRisks,
      recommendations: this.generateRecommendations(overallRisk, correlatedRisks)
    }
  }

  /**
   * 预测性维护分析
   * @param {Object} equipmentData - 设备历史数据
   * @param {Object} environmentData - 环境数据
   * @returns {Object} 预测结果
   */
  predictiveMaintenance(equipmentData, environmentData) {
    const predictions = {}

    // 塔吊预测分析
    for (const crane of equipmentData.cranes) {
      predictions[crane.id] = this.riskModels.predictCraneRisk(crane, environmentData)
    }

    // 高支模预测分析
    for (const scaffold of equipmentData.scaffolds) {
      predictions[scaffold.id] = this.riskModels.predictScaffoldRisk(scaffold, environmentData)
    }

    return {
      predictions,
      maintenanceSchedule: this.generateMaintenanceSchedule(predictions),
      criticalAlerts: this.identifyCriticalPredictions(predictions)
    }
  }

  /**
   * 应急预案匹配
   * @param {Object} emergencyData - 紧急情况数据
   * @returns {Object} 应急预案
   */
  matchEmergencyPlan(emergencyData) {
    const { type, severity, location, affectedSystems } = emergencyData

    const plan = this.knowledgeBase.getEmergencyPlan(type, severity)
    
    return {
      planId: plan.id,
      title: plan.title,
      steps: plan.steps,
      responsiblePersons: plan.getResponsiblePersons(location),
      resources: plan.getRequiredResources(affectedSystems),
      timeline: plan.getTimeline(severity),
      checkpoints: plan.getCheckpoints()
    }
  }

  /**
   * 智能问答处理
   * @param {string} question - 用户问题
   * @param {Object} context - 上下文数据
   * @returns {Object} 回答结果
   */
  processIntelligentQA(question, context) {
    const intent = this.parseIntent(question)
    const entities = this.extractEntities(question)

    switch (intent.type) {
      case 'RISK_INQUIRY':
        return this.handleRiskInquiry(entities, context)
      
      case 'EQUIPMENT_STATUS':
        return this.handleEquipmentStatus(entities, context)
      
      case 'WEATHER_FORECAST':
        return this.handleWeatherForecast(entities, context)
      
      case 'PERSONNEL_MANAGEMENT':
        return this.handlePersonnelManagement(entities, context)
      
      case 'EMERGENCY_GUIDANCE':
        return this.handleEmergencyGuidance(entities, context)
      
      default:
        return this.handleGeneralInquiry(question, context)
    }
  }

  // 私有方法实现
  assessWeatherRisk(weather) {
    const { windSpeed, visibility, temperature, humidity, forecast } = weather
    
    let riskLevel = 'low'
    const factors = []

    // 风速风险
    if (windSpeed > 15) {
      riskLevel = windSpeed > 25 ? 'high' : 'medium'
      factors.push(`风速${windSpeed}m/s，超过安全作业标准`)
    }

    // 能见度风险
    if (visibility < 5) {
      riskLevel = this.escalateRisk(riskLevel, 'medium')
      factors.push(`能见度${visibility}km，影响作业安全`)
    }

    // 预报风险
    if (forecast.some(f => f.windSpeed > 20)) {
      riskLevel = this.escalateRisk(riskLevel, 'medium')
      factors.push('未来24小时内预计有大风天气')
    }

    return {
      level: riskLevel,
      score: this.getRiskScore(riskLevel),
      factors,
      recommendations: this.getWeatherRecommendations(riskLevel, factors)
    }
  }

  assessStructuralRisk(cranes, scaffolds) {
    let maxRisk = 'low'
    const issues = []

    // 塔吊风险评估
    for (const crane of cranes) {
      if (crane.tilt > 1.5) {
        maxRisk = 'high'
        issues.push(`${crane.name}倾斜度${crane.tilt}°，超过报警阈值`)
      } else if (crane.tilt > 1.0) {
        maxRisk = this.escalateRisk(maxRisk, 'medium')
        issues.push(`${crane.name}倾斜度${crane.tilt}°，接近预警阈值`)
      }
    }

    // 高支模风险评估
    for (const scaffold of scaffolds) {
      const criticalPoints = scaffold.points.filter(p => p.status === 'danger')
      if (criticalPoints.length > 0) {
        maxRisk = 'high'
        issues.push(`${scaffold.name}有${criticalPoints.length}个测点超限`)
      }
    }

    return {
      level: maxRisk,
      score: this.getRiskScore(maxRisk),
      issues,
      recommendations: this.getStructuralRecommendations(maxRisk, issues)
    }
  }

  parseIntent(question) {
    const lowerQuestion = question.toLowerCase()
    
    if (lowerQuestion.includes('风险') || lowerQuestion.includes('危险')) {
      return { type: 'RISK_INQUIRY', confidence: 0.9 }
    }
    
    if (lowerQuestion.includes('塔吊') || lowerQuestion.includes('设备')) {
      return { type: 'EQUIPMENT_STATUS', confidence: 0.9 }
    }
    
    if (lowerQuestion.includes('天气') || lowerQuestion.includes('气象')) {
      return { type: 'WEATHER_FORECAST', confidence: 0.9 }
    }
    
    if (lowerQuestion.includes('人员') || lowerQuestion.includes('门禁')) {
      return { type: 'PERSONNEL_MANAGEMENT', confidence: 0.8 }
    }
    
    if (lowerQuestion.includes('应急') || lowerQuestion.includes('紧急')) {
      return { type: 'EMERGENCY_GUIDANCE', confidence: 0.8 }
    }
    
    return { type: 'GENERAL', confidence: 0.5 }
  }

  escalateRisk(currentRisk, newRisk) {
    const riskLevels = { low: 1, medium: 2, high: 3 }
    return riskLevels[newRisk] > riskLevels[currentRisk] ? newRisk : currentRisk
  }

  getRiskScore(level) {
    const scores = { low: 25, medium: 60, high: 90 }
    return scores[level] || 0
  }
}

/**
 * 知识库类 - 存储安全规范、操作手册、应急预案等
 */
class KnowledgeBase {
  constructor() {
    this.safetyStandards = this.initSafetyStandards()
    this.emergencyPlans = this.initEmergencyPlans()
    this.operationManuals = this.initOperationManuals()
  }

  initSafetyStandards() {
    return {
      windSpeed: {
        crane: { warning: 15, danger: 20 },
        scaffold: { warning: 12, danger: 18 },
        highWork: { warning: 10, danger: 15 }
      },
      tilt: {
        crane: { warning: 1.0, danger: 1.5 }
      },
      settlement: {
        scaffold: { warning: 3.0, danger: 5.0 }
      }
    }
  }

  initEmergencyPlans() {
    return {
      'WIND_EMERGENCY': {
        id: 'EP001',
        title: '大风天气应急预案',
        steps: [
          '立即通知所有高空作业人员停止作业',
          '塔吊操作员将吊钩收回并锁定',
          '检查临时设施加固情况',
          '人员撤离到安全区域',
          '持续监控风速变化'
        ]
      },
      'STRUCTURAL_EMERGENCY': {
        id: 'EP002',
        title: '结构安全应急预案',
        steps: [
          '立即停止相关区域所有作业',
          '疏散人员到安全区域',
          '通知结构工程师现场检查',
          '设置警戒线',
          '等待专业评估结果'
        ]
      }
    }
  }

  initOperationManuals() {
    return {
      'CRANE_OPERATION': {
        id: 'OM001',
        title: '塔吊安全操作手册',
        sections: {
          'pre_operation': {
            title: '作业前检查',
            items: [
              '检查塔吊结构完整性',
              '确认风速在安全范围内',
              '检查吊钩和钢丝绳状态',
              '确认作业区域无人员'
            ]
          },
          'operation': {
            title: '作业中注意事项',
            items: [
              '严格按照额定载荷作业',
              '密切关注风速变化',
              '保持与地面人员通讯',
              '发现异常立即停止作业'
            ]
          },
          'emergency': {
            title: '紧急情况处理',
            items: [
              '风速超限时立即停止作业',
              '设备故障时启动应急程序',
              '人员伤亡时立即救援',
              '及时上报安全事故'
            ]
          }
        }
      },
      'SCAFFOLD_OPERATION': {
        id: 'OM002',
        title: '高支模安全操作手册',
        sections: {
          'installation': {
            title: '安装要求',
            items: [
              '严格按照设计图纸施工',
              '确保立杆垂直度',
              '检查连接件紧固',
              '设置安全防护措施'
            ]
          },
          'monitoring': {
            title: '监测要求',
            items: [
              '定期检查沉降变化',
              '监控立杆倾斜度',
              '检查轴力分布',
              '记录监测数据'
            ]
          },
          'maintenance': {
            title: '维护保养',
            items: [
              '定期检查连接件',
              '及时更换损坏构件',
              '保持排水畅通',
              '防止超载使用'
            ]
          }
        }
      }
    }
  }

  getEmergencyPlan(type, severity) {
    return this.emergencyPlans[type] || this.emergencyPlans['GENERAL']
  }

  getOperationManual(equipmentType) {
    const manualMap = {
      'crane': 'CRANE_OPERATION',
      'scaffold': 'SCAFFOLD_OPERATION'
    }
    return this.operationManuals[manualMap[equipmentType]]
  }
}

/**
 * 风险模型类 - 实现各种预测算法
 */
class RiskModels {
  predictCraneRisk(crane, environment) {
    const { tilt, displacement, load } = crane
    const { windSpeed, temperature } = environment

    // 简化的风险预测模型
    let riskScore = 0
    
    // 倾斜度影响
    riskScore += tilt * 30
    
    // 风速影响
    riskScore += windSpeed * 2
    
    // 负载影响
    riskScore += load * 0.5

    return {
      equipmentId: crane.id,
      riskScore,
      riskLevel: this.scoreToLevel(riskScore),
      factors: this.identifyRiskFactors(crane, environment),
      prediction: this.generatePrediction(riskScore)
    }
  }

  scoreToLevel(score) {
    if (score > 80) return 'high'
    if (score > 50) return 'medium'
    return 'low'
  }

  identifyRiskFactors(crane, environment) {
    const factors = []

    if (crane.tilt > 1.0) {
      factors.push({
        type: 'structural',
        description: '塔吊倾斜度超过预警值',
        severity: crane.tilt > 1.5 ? 'high' : 'medium'
      })
    }

    if (environment.windSpeed > 15) {
      factors.push({
        type: 'environmental',
        description: '风速过大影响作业安全',
        severity: environment.windSpeed > 20 ? 'high' : 'medium'
      })
    }

    return factors
  }

  generatePrediction(riskScore) {
    if (riskScore > 80) {
      return {
        timeframe: '1-2小时内',
        probability: '高',
        recommendation: '立即停止作业，启动应急预案'
      }
    } else if (riskScore > 50) {
      return {
        timeframe: '4-6小时内',
        probability: '中等',
        recommendation: '加强监控，准备应急措施'
      }
    } else {
      return {
        timeframe: '24小时内',
        probability: '低',
        recommendation: '继续正常作业，保持监控'
      }
    }
  }
}

/**
 * 数据关联分析类
 */
class DataCorrelator {
  analyzeCorrelations(riskData) {
    const correlations = []

    // 天气与结构风险关联
    if (riskData.weather.level !== 'low' && riskData.structural.level !== 'low') {
      correlations.push({
        type: 'weather_structural',
        description: '大风天气加剧结构安全风险',
        severity: 'high',
        recommendation: '建议立即停止高空作业并加强结构监测'
      })
    }

    return correlations
  }
}

// 导出单例实例
export const aiEngine = new AIAnalysisEngine()
