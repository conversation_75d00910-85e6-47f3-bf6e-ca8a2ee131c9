/**
 * 角色化响应系统 - 为不同角色提供定制化的AI助手体验
 */

export class RoleBasedResponseSystem {
  constructor() {
    this.roles = {
      PROJECT_MANAGER: '项目经理',
      SAFETY_SUPERVISOR: '安全总监',
      CONSTRUCTION_LEADER: '施工队长',
      EQUIPMENT_OPERATOR: '设备操作员',
      SAFETY_OFFICER: '安全员'
    }
    
    this.currentRole = 'PROJECT_MANAGER' // 默认角色
  }

  /**
   * 设置当前用户角色
   */
  setRole(role) {
    this.currentRole = role
  }

  /**
   * 根据角色生成响应
   */
  generateRoleBasedResponse(intent, data, context) {
    switch (this.currentRole) {
      case 'PROJECT_MANAGER':
        return this.generateProjectManagerResponse(intent, data, context)
      
      case 'SAFETY_SUPERVISOR':
        return this.generateSafetySupervisorResponse(intent, data, context)
      
      case 'CONSTRUCTION_LEADER':
        return this.generateConstructionLeaderResponse(intent, data, context)
      
      case 'EQUIPMENT_OPERATOR':
        return this.generateEquipmentOperatorResponse(intent, data, context)
      
      default:
        return this.generateGeneralResponse(intent, data, context)
    }
  }

  /**
   * 项目经理专用响应 - 宏观决策与风险把控
   */
  generateProjectManagerResponse(intent, data, context) {
    if (intent === 'DAILY_BRIEFING') {
      return {
        text: `**早上好，王经理。今日施工风险简报：**\n\n` +
              `**主要风险点：**\n` +
              `1️⃣ **气象风险**：下午3点将有7级阵风，风力将超过塔吊安全作业标准，建议通知吊装作业班组在下午2点前完成收工。\n` +
              `2️⃣ **结构风险**：高支模区域的2号立杆轴力昨日有持续小幅增长，虽未超限，但已形成趋势，建议安排工程师进行现场复核。\n\n` +
              `**进度影响评估**：预计今日进度可能延缓15%，但安全风险可控。\n\n` +
              `相关数据趋势图已推送给您。`,
        
        data: [
          { label: '今日风险等级', value: '中等', status: 'warning' },
          { label: '预计进度影响', value: '-15%', status: 'warning' },
          { label: '安全状况', value: '可控', status: 'normal' },
          { label: '人员到岗率', value: '98%', status: 'normal' }
        ],
        
        actions: [
          { text: '查看详细风险报告', type: 'detailedRiskReport' },
          { text: '调整施工计划', type: 'adjustSchedule' },
          { text: '发送停工通知', type: 'sendStopWorkNotice' },
          { text: '召开安全会议', type: 'callSafetyMeeting' }
        ]
      }
    }

    if (intent === 'CONSTRUCTION_FEASIBILITY') {
      return {
        text: `**明日连续浇筑作业安全性评估：**\n\n` +
              `✅ **天气条件**：晴朗，风力小于4级，适宜施工\n` +
              `✅ **结构状态**：5个监测点的沉降和轴力数据在过去一周内均保持稳定\n` +
              `✅ **设备状态**：混凝土泵车、塔吊设备状态良好\n` +
              `✅ **人员配备**：浇筑班组已完成技术交底\n\n` +
              `**风险等级：低风险** ✅\n\n` +
              `**保障措施**：我将在浇筑过程中，将高支模监测频率提升至每分钟一次，并实时向您和安全总监推送沉降曲线，确保万无一失。`,
        
        data: [
          { label: '天气适宜性', value: '优', status: 'normal' },
          { label: '结构稳定性', value: '稳定', status: 'normal' },
          { label: '设备就绪度', value: '100%', status: 'normal' },
          { label: '综合风险', value: '低', status: 'normal' }
        ],
        
        actions: [
          { text: '确认浇筑计划', type: 'confirmConcretePlan' },
          { text: '设置监控频率', type: 'setMonitoringFreq' },
          { text: '通知相关班组', type: 'notifyTeams' }
        ]
      }
    }

    return this.generateGeneralManagerResponse(intent, data, context)
  }

  /**
   * 安全总监专用响应 - 实时监控与应急处置
   */
  generateSafetySupervisorResponse(intent, data, context) {
    if (intent === 'PROACTIVE_ALERT') {
      return {
        text: `🚨 **安全总监请注意！**\n\n` +
              `**关联风险预警**：检测到高空风速已达45公里/小时，同时1号塔吊的倾斜度在10分钟内增加了0.08°，已触发'风速与结构关联'预警模型。\n\n` +
              `**建议措施**：\n` +
              `⚠️ 立即通知塔吊操作员暂停作业\n` +
              `⚠️ 启动大风天气应急预案\n` +
              `⚠️ 疏散塔吊作业半径内人员\n\n` +
              `**应急联系人**：塔吊司机张师傅 138****1234`,
        
        data: [
          { label: '风速', value: '45km/h', status: 'danger' },
          { label: '塔吊倾斜变化', value: '+0.08°', status: 'danger' },
          { label: '预警级别', value: 'I级', status: 'danger' },
          { label: '影响范围', value: '1号塔吊区域', status: 'warning' }
        ],
        
        actions: [
          { text: '启动应急预案', type: 'activateEmergencyPlan' },
          { text: '通知塔吊停工', type: 'notifyCraneStop' },
          { text: '疏散人员', type: 'evacuatePersonnel' },
          { text: '联系应急小组', type: 'contactEmergencyTeam' }
        ]
      }
    }

    if (intent === 'INCIDENT_INVESTIGATION') {
      return {
        text: `**事件调查报告 - 南门闯入告警**\n\n` +
              `📹 **视频证据**：已为您锁定昨天下午3点15分告警时刻前后30秒的视频录像\n` +
              `👥 **现场人员**：根据门禁记录和区域人员定位，当时距离南门最近的班组是钢筋班组\n` +
              `📞 **负责人**：李师傅，联系电话138****5678\n` +
              `🕐 **时间线**：\n` +
              `• 15:14:30 - 检测到异常移动\n` +
              `• 15:15:00 - 触发闯入告警\n` +
              `• 15:15:15 - 安保人员响应\n\n` +
              `视频已在大屏分屏播放，请查看。`,
        
        data: [
          { label: '告警时间', value: '15:15:00', status: 'normal' },
          { label: '响应时间', value: '15秒', status: 'normal' },
          { label: '涉及人员', value: '钢筋班组', status: 'warning' },
          { label: '处置状态', value: '已处理', status: 'normal' }
        ],
        
        actions: [
          { text: '播放视频录像', type: 'playVideo' },
          { text: '联系班组负责人', type: 'contactTeamLeader' },
          { text: '生成事件报告', type: 'generateIncidentReport' },
          { text: '加强门禁管理', type: 'strengthenAccess' }
        ]
      }
    }

    return this.generateGeneralSafetyResponse(intent, data, context)
  }

  /**
   * 施工队长专用响应 - 班前提醒与过程保障
   */
  generateConstructionLeaderResponse(intent, data, context) {
    if (intent === 'PRE_WORK_BRIEFING') {
      return {
        text: `**李队长您好！高支模区域作业安全提醒：**\n\n` +
              `☀️ **天气状况**：今天天气良好，风力3级，适宜施工\n` +
              `📊 **监测数据**：您作业区域正上方的3号沉降监测点数据显示稳定\n` +
              `⚠️ **安全要求**：\n` +
              `• 物料堆放严禁超过500公斤/平方米\n` +
              `• 作业人员必须佩戴安全帽和安全带\n` +
              `• 严格按照作业指导书施工\n\n` +
              `📹 **智能监控**：我将通过视频AI识别监控该区域的堆载情况和人员安全行为\n\n` +
              `祝您施工顺利！有任何异常请及时联系。`,
        
        data: [
          { label: '天气适宜度', value: '良好', status: 'normal' },
          { label: '结构稳定性', value: '稳定', status: 'normal' },
          { label: '安全风险等级', value: '低', status: 'normal' },
          { label: 'AI监控状态', value: '已启用', status: 'normal' }
        ],
        
        actions: [
          { text: '查看作业指导书', type: 'viewWorkInstructions' },
          { text: '确认人员到位', type: 'confirmPersonnel' },
          { text: '开始安全交底', type: 'startSafetyBriefing' },
          { text: '联系安全员', type: 'contactSafetyOfficer' }
        ]
      }
    }

    if (intent === 'WORK_PROCESS_ALERT') {
      return {
        text: `🚨 **紧急通知 - 高支模班组**\n\n` +
              `**异常检测**：模板沉降速率异常，检测值超过安全阈值！\n\n` +
              `**立即执行**：\n` +
              `🛑 立即暂停所有作业\n` +
              `🏃‍♂️ 所有人员撤离到安全区域\n` +
              `📞 等待工程师现场检查\n` +
              `🚫 未经许可严禁重新进入作业区\n\n` +
              `**撤离路线**：从东侧安全通道撤离至材料堆场\n` +
              `**集合地点**：办公区前广场\n\n` +
              `工程师预计5分钟内到达现场。`,
        
        data: [
          { label: '沉降速率', value: '异常', status: 'danger' },
          { label: '风险等级', value: '高', status: 'danger' },
          { label: '撤离状态', value: '进行中', status: 'warning' },
          { label: '工程师ETA', value: '5分钟', status: 'normal' }
        ],
        
        actions: [
          { text: '确认人员撤离', type: 'confirmEvacuation' },
          { text: '联系工程师', type: 'contactEngineer' },
          { text: '设置警戒线', type: 'setWarningLine' },
          { text: '上报安全总监', type: 'reportToSafety' }
        ]
      }
    }

    return this.generateGeneralConstructionResponse(intent, data, context)
  }

  /**
   * 设备操作员专用响应 - 辅助安全操作
   */
  generateEquipmentOperatorResponse(intent, data, context) {
    if (intent === 'REAL_TIME_STATUS') {
      return {
        text: `**塔吊实时安全状态**\n\n` +
              `✅ **当前风速**：28km/h - 安全范围\n` +
              `✅ **实时倾斜度**：0.21° - 正常范围\n` +
              `✅ **当前载荷**：8.5吨 - 安全载荷\n` +
              `✅ **回转角度**：45° - 正常\n` +
              `✅ **起升高度**：25m - 安全高度\n\n` +
              `**操作建议**：当前条件良好，可正常作业`,
        
        data: [
          { label: '风速状态', value: '安全', status: 'normal' },
          { label: '倾斜状态', value: '正常', status: 'normal' },
          { label: '载荷状态', value: '安全', status: 'normal' },
          { label: '综合状态', value: '良好', status: 'normal' }
        ],
        
        actions: [
          { text: '查看历史数据', type: 'viewHistory' },
          { text: '设置提醒阈值', type: 'setAlertThreshold' },
          { text: '联系维保人员', type: 'contactMaintenance' }
        ]
      }
    }

    if (intent === 'CRITICAL_WARNING') {
      return {
        text: `🚨 **紧急警告！**\n\n` +
              `⚠️ **风速已达临界值！**\n` +
              `当前风速：42km/h（临界值：40km/h）\n\n` +
              `**立即执行**：\n` +
              `🛑 立即停止旋转操作\n` +
              `⬇️ 将吊钩收回到安全位置\n` +
              `🔒 锁定塔吊回转机构\n` +
              `📞 通知地面指挥人员\n\n` +
              `**预计持续时间**：30-45分钟\n` +
              `我将持续监控风速变化，条件改善后立即通知您。`,
        
        data: [
          { label: '当前风速', value: '42km/h', status: 'danger' },
          { label: '安全阈值', value: '40km/h', status: 'warning' },
          { label: '预计持续', value: '30-45分钟', status: 'warning' },
          { label: '操作状态', value: '必须停止', status: 'danger' }
        ],
        
        actions: [
          { text: '确认停止作业', type: 'confirmStopWork' },
          { text: '锁定设备', type: 'lockEquipment' },
          { text: '通知地面', type: 'notifyGround' },
          { text: '等待条件改善', type: 'waitForImprovement' }
        ]
      }
    }

    return this.generateGeneralOperatorResponse(intent, data, context)
  }

  /**
   * 通用响应生成
   */
  generateGeneralResponse(intent, data, context) {
    return {
      text: '我是工地助手，专为电厂施工安全监控而设计。我可以为不同角色提供专业的安全指导和决策支持。请告诉我您的角色和需要帮助的具体问题。',
      actions: [
        { text: '选择角色', type: 'selectRole' },
        { text: '风险分析', type: 'riskAnalysis' },
        { text: '设备监控', type: 'equipmentMonitor' }
      ]
    }
  }

  // 其他辅助方法...
  generateGeneralManagerResponse(intent, data, context) {
    return {
      text: '作为项目经理，您可以询问我关于项目进度、风险评估、资源调配等宏观管理问题。',
      actions: [
        { text: '项目风险评估', type: 'projectRiskAssessment' },
        { text: '进度影响分析', type: 'scheduleImpactAnalysis' },
        { text: '资源需求分析', type: 'resourceAnalysis' }
      ]
    }
  }

  generateGeneralSafetyResponse(intent, data, context) {
    return {
      text: '作为安全总监，您可以询问我关于安全风险、应急处置、事故调查等安全管理问题。',
      actions: [
        { text: '安全风险评估', type: 'safetyRiskAssessment' },
        { text: '应急预案查询', type: 'emergencyPlanQuery' },
        { text: '事故分析', type: 'accidentAnalysis' }
      ]
    }
  }
}

// 导出单例实例
export const roleResponseSystem = new RoleBasedResponseSystem()
