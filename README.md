# 电厂施工安全监控系统

一个专门针对电厂建设项目的大屏可视化安全监控系统，基于Vue.js开发，采用现代化的设计风格和响应式布局。

## 🎯 项目特点

- **专业化设计**: 针对电厂施工安全监控的专业界面
- **实时监控**: 塔吊、高支模、气象、人员等多维度实时监控
- **3D可视化**: 电厂施工区1:1三维模型展示
- **智能告警**: 多级告警系统，支持事件溯源和联动
- **数据融合**: 集成气象、地震、视频、门禁等多源数据

## 🏗️ 技术栈

- **Vue 3**: 渐进式JavaScript框架
- **Vite**: 现代化的前端构建工具
- **CSS3**: 现代CSS特性，包括Grid、Flexbox、动画等
- **SVG**: 矢量图形用于仪表盘和图表展示

## 📱 布局结构

### 整体布局
- **屏幕比例**: 16:9
- **基准分辨率**: 1920x1080px
- **布局方式**: 固定单屏布局

### 区域划分
- **顶部状态栏**: 高度80px，项目信息、安全评级、运行时长、AI助手
- **左侧面板**: 宽度25%，气象监测、天气预报、地震监测
- **中心区域**: 宽度50%，电厂施工区3D模型展示
- **右侧面板**: 宽度25%，塔吊监测、高支模监测、人员门禁
- **底部告警中心**: 高度120px，实时告警信息流

## 🎨 设计规范

### 颜色方案
- **主色调**: #40C4FF (亮天蓝)
- **辅助色**: #007BFF (标准蓝)
- **背景色**: #0A1122 (极暗近黑蓝)
- **面板背景**: #101A30 (深蓝灰，50%透明度)

### 状态色
- **重大风险**: #FF4D4F (亮红)
- **较大风险**: #FF8600 (橙色)
- **较小风险**: #FFC107 (黄色)
- **一般风险**: #40C4FF (蓝色)

### 字体规范
- **字体家族**: PingFang SC, Source Han Sans SC, sans-serif
- **基础字号**: 14px
- **标题字号**: H1(30px), H2(18px), H3(16px)

## 🚀 快速开始

### 安装依赖
```bash
npm install
```

### 开发模式
```bash
npm run dev
```

### 生产构建
```bash
npm run build
```

## 📦 项目结构

```
src/
├── components/          # 组件目录
│   ├── Header.vue      # 顶部标题组件
│   ├── LeftPanel.vue   # 左侧面板组件
│   ├── CenterMap.vue   # 中心地图组件
│   └── RightPanel.vue  # 右侧面板组件
├── assets/             # 静态资源
│   └── main.css       # 主样式文件
├── App.vue            # 主应用组件
└── main.ts           # 应用入口
```

## 🔧 功能模块

### 顶部状态栏
- **项目信息**: 项目名称、建设阶段显示
- **安全评级**: 综合安全评级（绿/黄/橙/红）
- **运行时长**: 系统安全运行时长统计
- **AI助手**: 智能助手入口

### 左侧气象监测
- **实时天气**: 地面/高空风速风向仪表盘、温度湿度气压
- **气象预报**: 24/72小时天气预报，大风沙尘预警
- **地震监测**: 实时PGA地震波数据，异常告警

### 中心3D模型
- **三维场景**: 电厂施工区1:1三维模型
- **设备状态**: 塔吊、高支模设备状态实时叠加
- **视频融合**: 摄像头图标点击调取实时视频
- **测点可视化**: GNSS、监测点精确标注
- **人员分布**: 各区域作业人员数量显示

### 右侧风险指标
- **塔吊监测**: 倾斜度、四项位移实时监控
- **高支模监测**: 立杆倾斜、轴力、沉降数据
- **人员门禁**: 场内人数、出勤统计、门禁记录

### 底部告警中心
- **实时告警**: I/II/III级告警信息滚动播放
- **事件溯源**: 点击告警联动3D模型和视频
- **告警统计**: 各级别告警数量实时统计

## 🎯 特色功能

1. **专业监控**: 针对电厂施工安全的专业化监控界面
2. **多源数据融合**: 气象、地震、视频、门禁等多源数据集成
3. **3D可视化**: 施工区三维模型，设备状态直观展示
4. **智能告警**: 多级告警系统，支持事件联动和溯源
5. **实时更新**: 所有监测数据实时更新，动态展示
6. **交互体验**: 丰富的交互功能，点击联动查看详情
7. **响应式设计**: 支持不同分辨率的自适应显示

## 📄 许可证

MIT License
